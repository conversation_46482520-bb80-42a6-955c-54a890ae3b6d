<template>
  <!-- 不显示任何DOM结构，只用于组件挂载 -->
  <div style="display: none;"></div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue';
import 'viewerjs/dist/viewer.css';
import Viewer from 'viewerjs';

interface ImageItem {
  url: string;
  name: string;
  size: string;
  bits: string;
}

interface Props {
  imageList: ImageItem[];
  currentImagePath?: string;
  visible: boolean;
  options?: any;
}

const props = withDefaults(defineProps<Props>(), {
  imageList: () => [],
  currentImagePath: '',
  visible: false,
  options: () => ({
    inline: false,
    viewed() {
      // 图片查看器打开时的回调
    },
    hidden() {
      // 图片查看器关闭时的回调
    }
  })
});

const emit = defineEmits<{
  (e: 'close'): void;
}>();

let viewer: Viewer | null = null;
let container: HTMLElement | null = null;

// 创建隐藏的容器用于图片预览
const createHiddenContainer = () => {
  // 如果已存在容器，先移除
  if (container) {
    document.body.removeChild(container);
  }
  
  // 创建新的隐藏容器
  container = document.createElement('div');
  container.className = 'image-viewer-hidden-container';
  container.style.cssText = `
    position: fixed;
    top: -9999px;
    left: -9999px;
    width: 1px;
    height: 1px;
    overflow: hidden;
    z-index: -1;
  `;
  
  // 为每张图片创建img元素
  props.imageList.forEach((image, index) => {
    const img = document.createElement('img');
    img.src = image.url || image.path || '';
    img.alt = index+'';
    img.style.cssText = 'max-width: 100%; height: auto;';
    container!.appendChild(img);
  });
  
  document.body.appendChild(container);
};

// 初始化查看器
const initViewer = () => {
  if (!container || props.imageList.length === 0) return;
  
  // 销毁之前的查看器
  if (viewer) {
    viewer.destroy();
    viewer = null;
  }
  
  // 创建新的查看器
  viewer = new Viewer(container, {
    ...props.options,
    toolbar: {
      zoomIn: 4,
      zoomOut: 4,
      oneToOne: 4,
      reset: 4,
      prev: 4,
      play: {
        show: 4,
        size: 'large',
      },
      next: 4,
      rotateLeft: 4,
      rotateRight: 4,
      flipHorizontal: 4,
      flipVertical: 4,
    },
    title: [1, (image: any, imageData: any) => {
      let index = image.alt;
  
      
      let name = props.imageList[index].name || '';
      let size = props.imageList[index].size || '';
      let bits = props.imageList[index].bits || '';
      let updatedAt = props.imageList[index].updated_at || '';
      
      return [
          '图片名称: ' + name,
          '上传时间: ' + updatedAt,
          '尺寸: ' + size,
          '大小: ' + bits
      ].join('; ');
    }],
    transition: true,
    keyboard: true,
    movable: true,
    zoomable: true,
    rotatable: true,
    scalable: true,
    toggleOnDblclick: true,
    tooltip: true,
    navbar: true,
    minZoomRatio: 0.1,
    maxZoomRatio: 10,
    hidden() {
      emit('close');
    }
  });
};

// 打开指定图片的预览
const openViewer = (imagePath?: string) => {
  if (!viewer || !container) return;
  
  if (imagePath) {
    // 找到指定图片的索引
    const index = props.imageList.findIndex(img => 
      (img.url || img.path) === imagePath
    );
    if (index !== -1) {
      viewer.view(index);
    } else {
      // 如果没找到指定图片，显示第一张
      viewer.view(0);
    }
  } else {
    // 如果没有指定图片，显示第一张
    viewer.view(0);
  }
};

// 监听visible变化
watch(() => props.visible, (newVisible) => {
  if (newVisible && props.imageList.length > 0) {
    nextTick(() => {
      createHiddenContainer();
      initViewer();
      openViewer(props.currentImagePath);
    });
  }
}, { immediate: true });

// 监听图片列表变化
watch(() => props.imageList, () => {
  if (props.visible && props.imageList.length > 0) {
    nextTick(() => {
      createHiddenContainer();
      initViewer();
      openViewer(props.currentImagePath);
    });
  }
}, { deep: true });

onMounted(() => {
  if (props.visible && props.imageList.length > 0) {
    nextTick(() => {
      createHiddenContainer();
      initViewer();
      openViewer(props.currentImagePath);
    });
  }
});

onUnmounted(() => {
  if (viewer) {
    viewer.destroy();
    viewer = null;
  }
  if (container) {
    document.body.removeChild(container);
    container = null;
  }
});
</script>

<style scoped>
/* 组件本身不显示任何样式 */
</style>
