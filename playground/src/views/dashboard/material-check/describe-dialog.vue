<script lang="ts" setup>
import { ref, createVNode } from 'vue';
import { Modal, Carousel, Textarea, message, Upload, Spin } from 'ant-design-vue';
import { 
  LeftCircleFilled, 
  RightCircleFilled, 
  DeleteOutlined,
  CheckCircleOutlined, 
  CloseOutlined, 
  ArrowUpOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons-vue';
import { storeToRefs } from 'pinia';
import { useMaterialStore } from '#/store';
import { useAuthStore } from '#/store/auth';
import { uploadImage } from '#/api/material-check';
import CommonViewerjs from './common-viewerjs.vue';
const materialStore = useMaterialStore();
const authStore = useAuthStore();
const uploadUrl = uploadImage();
const {
  sendAddEdit,
  deleteAddEdit,
  deleteEdit,
  toTagEdit
} = materialStore;
const { errorList } = storeToRefs(materialStore);
const describeRemark = ref(''); //差异描述
const uploadList = ref<any>([]);
const loading = ref(false);

// 图片预览相关状态
const previewVisible = ref(false);
const previewImages = ref<any[]>([]);
const currentImagePath = ref('');

const props = defineProps<{
  modelValue: boolean;
}>();
const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void;
  (e: 'update:goodsSn', value: string): void;
  (e: 'close'): void;
  (e: 'comfirm', data: any): void;
}>();
const handleUploadChange = async(info: any) => {
  // 根据上传状态设置loading
  if (info.file.status === 'uploading') {
    loading.value = true;
    return;
  }
  
  // 上传完成或失败时设置loading为false
  loading.value = false;
  
  let res = info.file.response

  if (res?.status != 1) return;
  message.success("图片上传成功");
  let path = res.data?.image_path;
  let url = res.data?.image_url;
  let name = res.data?.image_name;
  let bits = res.data?.bits;
  let size = res.data?.size;
  let updated_at = res.data?.updated_at;
  
  // 限制最多3张图片，重复插入时覆盖
  if (uploadList.value.length >= 3) {
    // 如果已经有3张图片，替换最后一张
    uploadList.value[uploadList.value.length - 1] = { path: url, name, bits, size, updated_at };
  } else {
    // 如果少于3张，直接添加
    uploadList.value.push({ path: url, name, bits, size, updated_at });
  }
};
const handleClose = () => {
  emit('update:modelValue', false);
  emit('close');
  uploadList.value = [];
  describeRemark.value = '';
};
const sendConfirm = () => {
  if(!describeRemark.value && uploadList.value.length === 0){
    message.warning('请输入差异描述或上传图片');
    return;
  }
  emit('comfirm', { remark: describeRemark.value, uploadList: uploadList.value });
  handleClose();
};
// 图片预览相关函数
const openImagePreview = (images: any[], imagePath: string) => {
  previewImages.value = images;
  currentImagePath.value = imagePath;
  previewVisible.value = true;
};

const closeImagePreview = () => {
  previewVisible.value = false;
  previewImages.value = [];
  currentImagePath.value = '';
};
</script>
<template>
<div>
  <!-- 图片预览组件 -->
    <CommonViewerjs
      :image-list="previewImages"
      :current-image-path="currentImagePath"
      :visible="previewVisible"
      @close="closeImagePreview"
    />
  <Modal 
      ref="diffmodalRef" 
      :footer="null" 
      :open="modelValue" 
      :destroyOnClose="true"
      wrapClassName="describeModal"
      width="600px"
      title="差异描述"
      @cancel="handleClose"
      :bodyStyle="{ 
        position: 'relative'
      }"
  >
  <div class="gap-line"></div>
  <Spin :spinning="loading">
    <div class="textarea-content">
      <Textarea
          v-model:value="describeRemark"
          placeholder="请勾选异常后详细描述差异内容～"
          :auto-size="{ minRows: 2, maxRows: 4 }"
          class="w-full h-full border-[1px] border-solid border-gray-300 rounded-md"
          @input="(e) => describeRemark = e.target.value"
        />
      <div class="img-content" v-if="uploadList.length > 0">
          <div v-for="item in uploadList" :key="item.path" class="img-item">
              <div class="img-wrapper" @click="openImagePreview(uploadList, item.path)">
                <img :src="item.path" :alt="item.name" class="preview-image" />
              </div>
          </div>
      </div>
    </div>
    <div class="form-footer">
      <Upload
        :maxCount="3"
        :multiple="true"
        :action="uploadUrl"
        :showUploadList="false"
        :headers="{Token: authStore.token, 'X-CSRF-TOKEN': authStore.token}"
        :data="{el_id: 'img_' + 1,  _token:authStore.token}"
        class="block w-full"
        @change="handleUploadChange"
      >
        <i class="font_family icon-image-error img_icon"></i>
      </Upload>
      <div class="send_icon" @click="sendConfirm"><ArrowUpOutlined /></div>
    </div>
  </Spin>
  </Modal>
</div>
</template>

<style scoped lang="scss">
.ant-input{
  border:none;
}
.ant-input:focus,.ant-input-focused{
  box-shadow: none !important;
}
.textarea-content{
  min-height: 220px;
  padding-bottom: 60px;
  overflow-y: auto;
}
.gap-line{
  width: 600px;
  height: 0;
  border-top: 1px solid #E7E7E7;
  margin-bottom: 10px;
  margin-left: -24px;
}
.img-content{
  min-height: 150px;
  padding: 10px 0;
  display: flex;
  margin-bottom: 10px;
  .img-item{
    margin-right: 10px;
    width: 120px !important;
  }
  .img-item:last-child{
    margin-right: 0;
  }
}
.form-footer{
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 50px;
  padding: 0 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 20px;
  box-sizing: border-box;
  border-top: 1px solid #E7E7E7;
  background-color: #fff;
  .img_icon{
    font-size: 24px;
    cursor: pointer;
  }
  .send_icon{
    cursor: pointer;
    width: 32px;
    height: 32px;
    background-color: #1890FF;
    border-radius: 50%;
    color: #fff;
    font-size: 16px;
    text-align: center;
    line-height: 32px;
  }
}
</style>
