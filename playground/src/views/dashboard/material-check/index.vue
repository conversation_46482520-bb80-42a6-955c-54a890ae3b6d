<script lang="ts" setup>
import { ref, createVNode, onMounted, computed } from 'vue';
import { useTemplateStore, useAuthStore, useMaterialStore } from '#/store';
import { storeToRefs } from 'pinia';
import { useRouter } from 'vue-router';
import { message, Input, Button, Carousel, Modal, Spin, Textarea, CheckboxGroup, Checkbox } from 'ant-design-vue';
import { EditOutlined, LeftCircleFilled, RightCircleFilled, ExclamationCircleOutlined } from '@ant-design/icons-vue';
import DetailDialog from './detail-dialog.vue';
import DescribeDialog from './describe-dialog.vue';
import CommonViewerjs from './common-viewerjs.vue';

interface ImageItem {
  url: string;
  name: string;
  size: string;
  bits: string;
}

interface MaterialDetailItem {
  nameFirst: string;
  name: string;
  descText: string;
  colorName: string;
  sellingPoint: string;
  uploadList: ImageItem[];
  selectlist: ImageItem[];
}

interface MaterialData {
  attr: string[];
  goods_sn: string;
  image: ImageItem[];
  goods_name?: string;
  desc?: string;
  sale_point?: string[];
  materialDetailList: MaterialDetailItem[];
}

const templateStore = useTemplateStore();
const { id, action_shop_page_id } = storeToRefs(templateStore);
const materialStore = useMaterialStore();
const { fetchInfo, loading, errorList, materialList } = storeToRefs(materialStore);
const { searchInfo, verifyPass, secondCheckConfirm, setErrorList, confirmAnomaly } = materialStore;

const authStore = useAuthStore();
const { token } = storeToRefs(authStore);
const router = useRouter();

const goodsSn = ref('');
// const materialList = ref<MaterialData[]>([]);
const detailDialogVisible = ref(false);
const currentDetailItem = ref<MaterialDetailItem | null>(null);
// 二次质检弹窗
const secondVisible = ref(false);
const secondConfirmItem = ref<MaterialDetailItem | null>(null);
const confirmRemark = ref('');
// 差异描述弹窗
const describeVisible = ref(false);
const currentItem = ref<any | null>(null);
// 异常checkbox
const checkboxGroup = ref<string[]>([]);
const showAbnormalCheckboxes = ref<{[key: string]: boolean}>({});
const abnormalCheckboxes = ref<{[key: string]: boolean}>({});

// 图片预览相关状态
const previewVisible = ref(false);
const previewImages = ref<ImageItem[]>([]);
const currentImagePath = ref('');
// 计算属性：提取图片数据
const getNewImages = (diffItem: any) => {
  return diffItem?.error?.find((err: any) => err.type == 2)?.new_images || [];
};

const getNewImagesLength = (diffItem: any) => {
  return getNewImages(diffItem).length;
};

// 根据errorList数据判断是否显示checkbox
const shouldShowCheckbox = (goodsSn: string) => {
  return showAbnormalCheckboxes.value[goodsSn] || errorList.value[goodsSn];
};

// 判断是否显示特定的checkbox
const shouldShowSpecificCheckbox = (goodsSn: string, type: string, field: string, fieldValue: string) => {
  // 如果用户已经点击了"确认异常"按钮，显示所有checkbox
  if (showAbnormalCheckboxes.value[goodsSn]) {
    return true;
  }
  // 否则只显示有数据的checkbox
  return isCheckboxChecked(goodsSn, type, field, fieldValue);
};

// 根据errorList数据判断checkbox是否默认选中
const isCheckboxChecked = (goodsSn: string, type: string, field: string, fieldValue: string) => {
  if (!errorList.value[goodsSn]) return false;
  
  const errorData = errorList.value[goodsSn];
  
  if (type === '1' || type === '3') {
    // 查找type为1或3的数据
    const targetError = errorData.find((err: any) => err.type == type);
    if (targetError && targetError.list) {
      return targetError.list.some((item: any) => 
        item.field === field && item.field_value === fieldValue
      );
    }
  } else if (type === '2') {
    // 查找type为2的数据，比对error_images
    const targetError = errorData.find((err: any) => err.type == 2);
    if (targetError && targetError.error_images) {
      return targetError.error_images.some((item: any) => 
        item.path === field && item.name === fieldValue
      );
    }
  }
  
  return false;
};

onMounted(async () => {
  token.value = router.currentRoute.value.query.token as string;
  id.value = router.currentRoute.value.query.id as string;
  action_shop_page_id.value = router.currentRoute.value.query.action_shop_page_id as string;
});

const handlePhysicalCheck = async () => {
  if (!goodsSn.value.trim()) {
    return;
  }
  const sn = goodsSn.value.trim();
  searchInfo({ goods_sn: sn, action_shop_page_id: action_shop_page_id.value})
};

const showDetail = (goodsSn: string, id: string|number) => {
  currentDetailItem.value = { goods_sn: goodsSn, id: id } as any;
  detailDialogVisible.value = true;
};

const handleDetailClose = () => {
  currentDetailItem.value = null;
  detailDialogVisible.value = false;
};
// 校验通过
const verify = async (obj: any) => { 
  verifyPass(obj);
}
// 二次质检
const secondCheck = async (obj: any) => {
  secondVisible.value = true;
  secondConfirmItem.value = obj;
}
// 二次质检确认
const handleConfirm = async () => {
  if (!confirmRemark.value) {
    message.warning('请输入备注信息');
    return;
  }
  secondCheckConfirm({
    goods_sn: secondConfirmItem.value?.goods_sn, 
    remarks: confirmRemark.value, 
    action_shop_page_id: action_shop_page_id.value
  });
  secondVisible.value = false;
  confirmRemark.value = '';
}

// 确认异常
const handleAbnormalCheck = async (item: MaterialData) => {
  const goodsSn = item.goods_sn;
  console.log('goodsSn', item);
  
  // 显示所有checkbox
  showAbnormalCheckboxes.value[goodsSn] = true;
};

// 确认异常按钮
const handleConfirmAbnormal = async (item: MaterialData) => {
  const goodsSn = item.goods_sn;
  
  // 根据errorList判断是否有选中的checkbox
  const hasCheckedItems = isCheckedAbnormal(goodsSn);
  
  if (!hasCheckedItems) {
    message.warning('请先勾选异常内容');
    return;
  }
  
  console.log('确认异常，errorList数据:', errorList.value[goodsSn]);
  describeVisible.value = true;
  currentItem.value = item;
};
// 处理异常勾选框变化
const handleAbnormalChange = (key: string, fieldKeys, field_value: string, goods_sn: string, checked: boolean, img?:object) => {
  abnormalCheckboxes.value[key] = checked;
  let keyArr = key.split('_');
  let type = keyArr[0];
  let field = fieldKeys;
  let goodsSn = goods_sn;
  
  // 确保当前商品的errorList存在
  if (!errorList.value[goodsSn]) {
    errorList.value[goodsSn] = [{type: '1', list:[]},{type: '2', new_images:[],error_images:[] },{type: '3', list:[]}];
  }
  
  if (checked) {
    // 勾选时添加数据
    if (type === '1' || type === '3') {
      // type 为 1 或 3 时，在 list 中插入数据
      const targetError = errorList.value[goodsSn].find(item => item.type === type);
      if (targetError && targetError.list) {
        targetError.list.push({
          field: field,
          field_value: field_value
        });
      }else{
        targetError.list = [{field: field,field_value: field_value}];
      }
    } else if (type === '2') {
      // type 为 2 时，在 error_images 中插入数据
      const targetError = errorList.value[goodsSn].find(item => item.type === '2');
      let imgInfo = img?.image_info ? JSON.parse(img?.image_info) : {};
      if (targetError && targetError.error_images) {
        targetError.error_images.push({
          path: field,
          name: field_value,
          bits: imgInfo?.bits,
          size: imgInfo?.size,
          updated_at: imgInfo?.updated_at
        });
      }else{
        targetError.error_images = [{
          path: field,
          name: field_value,
          bits: imgInfo?.bits,
          size: imgInfo?.size,
          updated_at: imgInfo?.updated_at
        }];
      }
    }
  } else {
    // 取消勾选时移除数据
    if (type === '1' || type === '3') {
      const targetError = errorList.value[goodsSn].find(item => item.type === type);
      if (targetError && targetError.list) {
        targetError.list = targetError.list.filter(item => 
          !(item.field === field && item.field_value === field_value)
        );
      }
    } else if (type === '2') {
      const targetError = errorList.value[goodsSn].find(item => item.type === '2');
      if (targetError && targetError.error_images) {
        targetError.error_images = targetError.error_images.filter(item => 
          !(item.path === field && item.name === field_value)
        );
      }
    }
  }
  setErrorList(errorList.value[goodsSn], goodsSn);
 
}
const handleDiffClose = () => {
  describeVisible.value = false;
}
const handleDiffConfirm = async (data: any) => {

  // 根据errorList判断是否有选中的checkbox
  const hasCheckedItems = isCheckedAbnormal(currentItem.value.goods_sn);
  if (!hasCheckedItems) {
    message.warning('请先勾选异常内容');
    return;
  }
  handleDiffClose();
  const respon = await confirmAnomaly(currentItem?.value.goods_sn, data, action_shop_page_id.value);
  // 如果请求成功，隐藏当前material-item容器下没有被勾选的checkbox
  if (respon?.status === 1) {
    const goodsSn = currentItem?.value.goods_sn;
    if (goodsSn) {
      // 隐藏该商品的异常勾选框显示状态
      showAbnormalCheckboxes.value[goodsSn] = false;
      // 清除该商品的异常勾选状态
      Object.keys(abnormalCheckboxes.value).forEach(key => {
        if (key.endsWith(`_${goodsSn}`)) {
          abnormalCheckboxes.value[key] = false;
        }
      });
    }
  }
}
// 判断是否勾选异常
const isCheckedAbnormal = (goodsSn: string) => {
  const hasCheckedItems = errorList.value[goodsSn] && errorList.value[goodsSn].some((errorItem: any) => {
    if (errorItem.type === '1' || errorItem.type ==='3') {
      return errorItem.list && errorItem.list.length > 0;
    } else if (errorItem.type === '2') {
      return errorItem.error_images && errorItem.error_images.length > 0;
    }
    return false;
  });
  return hasCheckedItems;
}

// 图片预览相关函数
const openImagePreview = (images: ImageItem[], imagePath: string, imgType: string) => {
  let _images = images
  if(imgType == 'img_list'){
    _images = images.map((image) => {
        let imageInfo = image.image_info ? JSON.parse(image.image_info) : {};
        return {
          url: image.image_path,
          name: image.image_name,
          size: imageInfo.size,
          bits: imageInfo.bits,
          updated_at: imageInfo.updated_at,
        };
      });
  }
  previewImages.value = _images;
  currentImagePath.value = imagePath;
  previewVisible.value = true;
};

const closeImagePreview = () => {
  previewVisible.value = false;
  previewImages.value = [];
  currentImagePath.value = '';
};
</script>

<template>
  <div class="h-full w-full">
    <div class="flex h-full w-full flex-col">
      <div class="p-4">
        <div class="mb-4 search-div">
          <div class="search-con">
            <span>货号：</span>
             <Input
              v-model:value="goodsSn"
              placeholder="请输入商品编号"
              class="w-64"
              @keyup.enter="handlePhysicalCheck"
            >
            </Input>
          </div>
         
          <Button class="search-btn" :loading="loading" @click="handlePhysicalCheck">
              查询
          </Button>
        </div>

        <div class="content-div">
          <template v-if="materialList.length === 0">
            <div class="text-center text-gray-500">暂无查询数据</div>
          </template>
       
          <template v-else>
             <Spin :spinning="loading">
             
            <div
              v-for="(item, index) in materialList"
              :key="item.goods_sn"
              :id="item.goods_sn"
              class="material-item"
            >
            <!-- 左侧差异描述内容框 -->
              <div class="material-content scroll" v-if="item.check_log?.diff">
                <div 
                  v-for="(diffItem, diffIndex) in item.check_log?.diff ? JSON.parse(item.check_log.diff)  : []"
                  :key="diffItem.key"
                  class="material-content-item">
                  <div v-if="diffItem.is_del != 1" style="border-bottom: 1px solid #E7E7E7;">
                    <div class="content-head">
                      <div class="tip">{{ diffItem.operator?.split('')[0] }}</div>
                      <div class="tip-name">{{ diffItem.operator }}</div>
                      <div class="tip-time">{{ diffItem.time }}</div>
                      <div class="tip-edit" v-if="diffIndex==0" @click="showDetail(item.goods_sn, item.check_log?.id)">
                        <EditOutlined />
                      </div>
                    </div>
                    <div class="content-desc" :title="diffItem.content">{{ diffItem.content }}</div>
                    
                    <div class="content-img-list" >
                      <div v-if="getNewImagesLength(diffItem) < 3" class="less-img-list">
                          <div v-for="carouselItem in getNewImages(diffItem)" :key="carouselItem.path" class="img-item">
                            <div class="img-wrapper" @click="openImagePreview(getNewImages(diffItem), carouselItem.path, 'check_log')">
                              <img :src="carouselItem.path" :alt="carouselItem.name" class="preview-image" />
                            </div>
                          </div>
                      </div>
                      <Carousel 
                        v-else
                        :slidesToShow="2"
                        :dots="false"
                        arrows
                        class="custom-carousel"
                      >
                        <template #prevArrow>
                          <div class="custom-slick-arrow custom-slick-prev">
                            <LeftCircleFilled />
                          </div>
                        </template>
                        <template #nextArrow>
                          <div class="custom-slick-arrow custom-slick-next">
                            <RightCircleFilled />
                          </div>
                        </template>
                          <div v-for="carouselItem in getNewImages(diffItem)" :key="carouselItem.path" class="img-item">
                            <div class="img-wrapper" @click="openImagePreview(getNewImages(diffItem), carouselItem.path, 'check_log')">
                              <img :src="carouselItem.path" :alt="carouselItem.name" class="preview-image" />
                            </div>
                          </div>
                        
                      </Carousel>
                    </div>
                  </div>
                </div>
              </div>
              <!-- 红框内容 -->
              
              <div class="material-header">
                <div class="sort">{{ index + 1 }}.</div>
                <div class="goods-sn">{{ item.goods_sn }}</div>
              </div>

              <div class="title">
                {{ Object.values(item.goods_name || {})[0] || item.goods_name }}
                <span 
                  v-if="Object.values(item.goods_name || {})[0] && shouldShowSpecificCheckbox(item.goods_sn, '1', Object.keys(item.goods_name || {})[0], Object.values(item.goods_name || {})[0])" 
                  :class="['common-check', { 'checked': isCheckboxChecked(item.goods_sn, '1', Object.keys(item.goods_name || {})[0], Object.values(item.goods_name || {})[0]) || abnormalCheckboxes[`1_${Object.keys(item.goods_name || {})[0]}_${item.goods_sn}`] }]">
                  <Checkbox 
                    :checked="isCheckboxChecked(item.goods_sn, '1', Object.keys(item.goods_name || {})[0], Object.values(item.goods_name || {})[0])"
                    @change="(e) => handleAbnormalChange(`1_${Object.keys(item.goods_name || {})[0]}_${item.goods_sn}`,Object.keys(item.goods_name || {})[0], Object.values(item.goods_name || {})[0], item.goods_sn, e.target.checked)"
                  >异常</Checkbox>
                </span>
              </div>

              <div class="attr-section">
                <div class="left-section">
                  <div>
                    {{ Object.values(item.desc || {})[0] || item.desc }}
                    <span 
                      v-if="Object.values(item.desc || {})[0] && shouldShowSpecificCheckbox(item.goods_sn, '1', Object.keys(item.desc || {})[0], Object.values(item.desc || {})[0])" 
                      :class="['common-check', { 'checked': isCheckboxChecked(item.goods_sn, '1', Object.keys(item.desc || {})[0], Object.values(item.desc || {})[0]) || abnormalCheckboxes[`1_${Object.keys(item.desc || {})[0]}_${item.goods_sn}`] }]">
                      <Checkbox 
                        :checked="isCheckboxChecked(item.goods_sn, '1', Object.keys(item.desc || {})[0], Object.values(item.desc || {})[0])"
                        @change="(e) => handleAbnormalChange(`1_${Object.keys(item.desc || {})[0]}_${item.goods_sn}`, Object.keys(item.desc || {})[0], Object.values(item.desc || {})[0], item.goods_sn, e.target.checked)"
                      >异常</Checkbox>
                    </span>
                  </div>
                </div>
                <div class="right-section">
                  <div
                    v-for="(value, key) in item.sale_point"
                    :key="key"
                    class="attr-content"
                  >
                    <span class="dot"></span>
                    {{ value }}
                    <span 
                      v-if="shouldShowSpecificCheckbox(item.goods_sn, '1', key, value)" 
                      :class="['common-check', { 'checked': isCheckboxChecked(item.goods_sn, '1', key, value) || abnormalCheckboxes[`1_${key}_${item.goods_sn}`] }]">
                      <Checkbox 
                        :checked="isCheckboxChecked(item.goods_sn, '1', key, value)"
                        @change="(e) => handleAbnormalChange(`1_${key}_${item.goods_sn}`, key, value,item.goods_sn, e.target.checked)"
                      >异常</Checkbox>
                    </span>
                  </div>
                </div>
              </div>

              <div class="attr-section">
                <div class="left-section attr">
                  <div class="attr-title">非文案的商品属性质检</div>
                  <div
                    v-for="(value, key) in item.attr"
                    :key="key"
                    class="attr-content"
                  >
                    <span class="dot"></span>
                    {{ value }}
                    <span v-if="shouldShowSpecificCheckbox(item.goods_sn, '3', key, value)" :class="['common-check', { 'checked': isCheckboxChecked(item.goods_sn, '3', key, value) || abnormalCheckboxes[`3_${key}_${item.goods_sn}`] }]">
                      <Checkbox 
                        :checked="isCheckboxChecked(item.goods_sn, '3', key, value)"
                        @change="(e) => handleAbnormalChange(`3_${key}_${item.goods_sn}`,  key, value, item.goods_sn, e.target.checked)"
                      >异常</Checkbox>
                    </span>
                  </div>
                </div>
              </div>

              <div class="image-section">
                <div
                  v-for="img in item.image"
                  :key="img.image_path"
                  class="image-item"
                >
                  <div class="image-container">
                    <div class="image-sort">
                      {{ img.sort }}.
                      <span 
                        v-if="shouldShowSpecificCheckbox(item.goods_sn, '2', img.image_path, img.image_name)" 
                        :class="['common-check', { 'checked': isCheckboxChecked(item.goods_sn, '2', img.image_path, img.image_name) || abnormalCheckboxes[`2_${img.image_path}_${item.goods_sn}`] }]">
                        <Checkbox 
                          :checked="isCheckboxChecked(item.goods_sn, '2', img.image_path, img.image_name)"
                          @change="(e) => handleAbnormalChange(`2_${img.image_path}_${item.goods_sn}`, img.image_path, img.image_name, item.goods_sn, e.target.checked, img)"
                        >异常</Checkbox>
                      </span>
                    </div>
                    <img
                      :src="img.image_path"
                      :alt="img.image_name"
                      class="preview-image"
                      @click="openImagePreview(item.image, img.image_path, 'img_list')"
                    />
                    <div class="image-name">{{ img.image_name }}</div>
                  </div>
                </div>
              </div>
              <div class="btns-section">
                <Button class="btns-btn primary" @click="verify(item)">
                  校验通过
                </Button>
                <Button 
                  v-if="!showAbnormalCheckboxes[item.goods_sn]" 
                  class="btns-btn" 
                  @click="handleAbnormalCheck(item)"
                >
                校验不通过
                  
                </Button>
                <Button 
                  v-if="showAbnormalCheckboxes[item.goods_sn]" 
                  class="btns-btn" 
                  @click="handleConfirmAbnormal(item)"
                >
                  确认异常
                </Button>
                 <Button class="btns-btn" @click="secondCheck(item)">
                  二次质检
                </Button>
              </div>
            </div>
             
            </Spin>
          </template>
        </div>
      </div>
    </div>
    <!-- 差异描述详情弹窗 -->
    <DetailDialog
      v-model="detailDialogVisible"
      :detail-item="currentDetailItem"
      @close="handleDetailClose"
      @update:model-value="(val) => detailDialogVisible = val"
    />
     <!-- 差异描述 -->
    <DescribeDialog 
       v-model="describeVisible"
       @close="handleDiffClose"
       @comfirm="handleDiffConfirm"
       @update:model-value="(val) => describeVisible = val"
    />
    
    <!-- 图片预览组件 -->
    <CommonViewerjs
      :image-list="previewImages"
      :current-image-path="currentImagePath"
      :visible="previewVisible"
      @close="closeImagePreview"
    />
    
    <Modal 
      ref="modalRef" 
      v-model:open="secondVisible" 
      title="备注" 
      @ok="handleConfirm"
      :bodyStyle="{ 
        height: '150px'
      }"
    >
      <Textarea
        v-model:value="confirmRemark"
        placeholder="请详细描述二次质检备注～"
        :auto-size="{ minRows: 6, maxRows: 6 }"
        class="w-full h-full border-[1px] border-solid border-gray-300 rounded-md"
        @input="(e) => confirmRemark = e.target.value"
      />
    </Modal>
  </div>
</template>

<style scoped lang="scss">
.search-div{
  width: 100%;
  height: 106px;
  background-color: #F0F2F5;
  padding: 16px;
  box-sizing: border-box;
  .search-con{
    display: flex;
  }
}
.less-img-list{
  display: flex;
    .img-item{
      margin-right: 10px;
      width: 50%;
    }
    .img-item:last-child{
      margin-right: 0;
    }
}
.search-btn{
  width: 60px;
  height: 32px;
  background-color: #1890ff;
  color: #fff;
  border-radius: 3px;
  margin: 10px 44px;
  border: none;
  cursor: pointer;
}
.search-btn:hover{
  background-color: #1890ff;
  color: #fff;
}
.content-div {
  text-align: center;
  font-size: 14px;
  padding-bottom: 20px;
  overflow-y: auto;
}

.material-item {
  border: 1px solid #FF0000;
  clear: both;
  margin-bottom: 10px;
  position: relative;
  min-height: 350px;
  padding-bottom: 75px;
}

.material-content{
  position: absolute;
  z-index: 10;
  top: 20px;
  left: 28px;
  width: 20%;
  height: 305px;
  border: 1px solid #E7E7E7;
  padding: 15px;
  box-sizing: border-box;
  background-color: #fff;
  box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.16);
  overflow-y: auto;
  .material-content-item{
    padding-bottom: 10px;
    margin-bottom: 10px;
    .content-head{
      display: flex;
      align-items: center;
      gap: 10px;
      .tip{
        width: 32px;
        height: 32px;
        background-color: #9847FF;
        border-radius: 50%;
        color: #fff;
        font-size: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .tip-name{
        font-size: 18px;
        font-weight: 500;
        color: #000;
      }
      .tip-time{
        font-size: 14px;
        color: rgba(0,0,0,0.4);
      }
      .tip-edit{
        justify-self: flex-end;
        margin-left: auto;
        cursor: pointer;
        font-size: 16px;
      }
    }
    .content-desc{
      font-size: 16px;
      color: rgba(0,0,0,0.8);
      margin: 10px 0;
      padding-left: 40px;
      text-align: left;
      box-sizing: border-box;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2; /* 限制显示两行 */
      overflow: hidden;
      text-overflow: ellipsis; /* 超出部分显示省略号 */
    }
    .content-img-list{
      padding-left: 35px;
      .custom-carousel {
        :deep(.slick-slide) {
          padding: 0 2px;
        }
        :deep(.slick-list) {
          margin: 0 -2px;
        }
      }
      .custom-slick-arrow {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        z-index: 1;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        color: rgba(0,0,0,0.2);
        cursor: pointer;
        &:hover {
          background: rgba(0, 0, 0, 0.5);
        }
        .anticon{
          font-size: 24px;
        }
      }
      .custom-slick-prev {
        left: 7px;
      }
      .custom-slick-next {
        right: 7px;
      }
      .img-item {
        width: 140px;
        height: 140px;
        .img-wrapper {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          overflow: hidden;
        }
        .preview-image {
          max-width: 100%;
          max-height: 100%;
          width: auto;
          height: auto;
          object-fit: contain;
          cursor: pointer;
          transition: transform 0.2s ease;
          
          &:hover {
            transform: scale(1.05);
          }
        }
      }
    }
  }
}
.material-header {
  height: 38px;
  line-height: 34px;
  display: flex;
  justify-content: space-between;
  padding: 0 10px;
}

.sort {
  font-size: 18px;
}

.goods-sn {
  background: #FF0000;
  font-size: 19px;
  padding: 0 10px;
  color: #ffffff;
  position: relative;
  right: -10px;
}

.attr-section {
  margin: 20px 15%;
  display: flex;
}

.left-section {
  width: 40%;
  margin-left: 12%;
  text-align: left;
  min-height: 20px;
}

.right-section {
  width: 40%;
  margin-left: 40px;
}

.attr {
  border: 1px dashed #e6e6e6;
}

.attr-content {
  text-align: left;
  margin-bottom: 5px;
  display: flex;
  align-items: center;
}

.dot {
  width: 8px;
  height: 8px;
  background-color: #000;
  border-radius: 50%;
  margin: 0 20px;
  display: inline-block;
}

.attr-title {
  text-align: left;
  padding-left: 10px;
  font-size: 16px;
  margin-bottom: 5px;
}

.image-section {
  padding: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

.image-item {
  margin: 10px 20px 0 0;
}

.image-container {
  position: relative;
  text-align: left;
}

.image-sort {
  text-align: left;
  margin-bottom: 5px;
}

.preview-image {
  width: 250px;
  height: 250px;
  z-index: 9;
  object-fit: cover;
  cursor: pointer;
  transition: transform 0.2s ease;
  
  &:hover {
    transform: scale(1.05);
  }
}

.image-name {
  width: 250px;
  height: 250px;
  position: absolute;
  left: 0;
  top: 0;
  line-height: 250px;
  color: #1890ff;
  z-index: -5;
}
.btns-section{
  padding: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  width: 100%;
  border-top: 1px solid #E7E7E7;
  position: absolute;
  bottom: 0;
  left: 0;
  .btns-btn{
    height: 32px;
    padding: 5px 16px;
    border-radius: 3px;
    cursor: pointer;
    background-color: #F5F5F5;
    color: rgba(0,0,0,0.8);
    border: 1px solid #E7E7E7;
    &:hover{
      background-color: #F5F5F5;
      color: rgba(0,0,0,0.8);
    }
  }
  .btns-btn.primary{
    background-color: #1890ff;
    color: #fff;
    &:hover{
      background-color: #1890ff;
      color: #fff;
    }
  }
}
// 公共勾选异常样式
.common-check{
  border-radius: 34px;
  border: 1px solid #e7e7e7;
  padding: 0 2px 0 6px;
  display: inline-block;
  margin-left: 10px;
  transition: border-color 0.3s ease;
  
  &.checked {
    border-color: red;
  }
  
  :deep(.ant-checkbox:hover .ant-checkbox-inner){
      border-color: red !important;
  }
   :deep(.ant-checkbox-inner){
    border-radius: 50% !important;
  }
  :deep(.ant-checkbox-checked .ant-checkbox-inner){
    background-color: red !important;
    border-color: red !important;
  }
  :deep(.ant-checkbox-checked+span){
    color: red;
  }
   :deep(.ant-checkbox-wrapper:hover span){
    color: red;
  }
  :deep(.ant-checkbox-wrapper:hover .ant-checkbox-checked .ant-checkbox-inner){
    border-color: red !important;
  }
  :deep(.ant-checkbox-wrapper:hover .ant-checkbox-checked:after ){
    border-color: red !important;
  }
  :deep(.ant-checkbox-wrapper:not(.ant-checkbox-wrapper-disabled):hover .ant-checkbox-inner, .ant-checkbox:not(.ant-checkbox-disabled):hover .ant-checkbox-inner){
    border-color: red;
  }
  :deep(:where(.css-dev-only-do-not-override-14589v).ant-checkbox-checked:after){
     border-color: red;
  }
}
</style>
