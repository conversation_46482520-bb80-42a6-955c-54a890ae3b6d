<script lang="ts" setup>
import { ref, createVNode, computed, watch } from 'vue';
import { Modal, Carousel, Textarea, Upload, message, Spin } from 'ant-design-vue';
import { 
  LeftCircleFilled, 
  RightCircleFilled, 
  DeleteOutlined,
  CheckCircleOutlined, 
  CloseOutlined, 
  ArrowUpOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons-vue';
import { useMaterialStore } from '#/store';
import { useAuthStore } from '#/store/auth';
import { uploadImage } from '#/api/material-check';
import { storeToRefs } from 'pinia';
import CommonViewerjs from './common-viewerjs.vue';

const authStore = useAuthStore();
const uploadUrl = uploadImage();
const materialStore = useMaterialStore();
const {
  sendAddEdit,
  deleteOneEdit,
  deleteEdit,
  toTagAllEdit
} = materialStore;
const { errorList, modalLoading} = storeToRefs(materialStore);
const { materialList } = storeToRefs(materialStore);

interface UploadItem {
  path: string;
  name: string;
  bits: string;
  size: string;
  updated_at: string;
}

interface DetailItem {
  id?: number;
  goods_sn?: string;
}

const props = defineProps<{
  modelValue: boolean;
  detailItem: DetailItem | null;
}>();
const textDesc = ref('');
const uploadList = ref<any>([]);
const loading = ref(false);
const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void;
  (e: 'close'): void;
}>();
// 图片预览相关状态
const previewVisible = ref(false);
const previewImages = ref<any[]>([]);
const currentImagePath = ref('');

// 根据goods_sn找到对应的diff数据
const diffData = computed(() => {
  if (!props.detailItem?.goods_sn || !materialList.value) {
    return [];
  }
  
  const targetItem = materialList.value.find(item => item.goods_sn === props.detailItem?.goods_sn);
  if (!targetItem) {
    return [];
  }
  // 直接使用check_log.diff
  return targetItem.check_log?.diff ? JSON.parse(targetItem.check_log.diff)  : [];
});

// 当前选中的diffItem
const currentDiffItem = ref<any>(null);

// 根据传入的diffItem获取new_images
const getNewImages = (diffItem: any) => {
  return diffItem?.error?.find((err: any) => err.type == 2)?.new_images || [];
};

// 根据传入的diffItem获取error_images
const getErrorImages = (diffItem: any) => {
  return diffItem?.error?.find((err: any) => err.type == 2)?.error_images || [];
};

// 根据传入的diffItem获取type1数据
const getType1Data = (diffItem: any) => {
  return diffItem?.error?.find((err: any) => err.type == 1)?.list || [];
};

// 根据传入的diffItem获取type3数据
const getType3Data = (diffItem: any) => {
  return diffItem?.error?.find((err: any) => err.type == 3)?.list || [];
};
// 监听diffData变化，自动设置第一个diffItem为当前选中项
watch(diffData, (newDiffData) => {
  if (newDiffData.length > 0) {
    currentDiffItem.value = newDiffData[0];
  } else {
    currentDiffItem.value = null;
  }
}, { immediate: true });

const handleClose = () => {
  emit('update:modelValue', false);
  emit('close');
};
const deleteDetailItem = (item: any) => {
  Modal.confirm({
    title: '你确定删除吗?',
    icon: createVNode(ExclamationCircleOutlined),
    content: createVNode('div', { style: 'color:red;' }, '删除后不可恢复'),
    async onOk() {
      try {
        loading.value = true;
        
        // 根据goods_sn找到对应的商品数据
        const targetItem = materialList.value.find(material => material.goods_sn === props.detailItem?.goods_sn);
        let goods_sn = props.detailItem?.goods_sn;
        let id = props.detailItem?.id;
        const res = await deleteOneEdit({goods_sn, id, key:item.key});
        // 如果删除的是当前选中的diffItem，需要重新设置currentDiffItem
        if (currentDiffItem.value?.key === item.key) {
          if (targetItem.check_log.diff.length > 0) {
            currentDiffItem.value = targetItem.check_log.diff[0];
          } else {
            currentDiffItem.value = null;
          }
        }
        setTimeout(()=>{
           if(!diffData.value.length){
              handleClose();
            }
        }, 1500)
      } catch (err) {
        console.error('删除失败:', err);
      } finally {
        loading.value = false;
      }
    },
    onCancel() {
      console.log('Cancel');
    },
    class: 'test',
  });
}
// const handleDeleteOne = (detailId: number, obj: object, index: any) => {
//   let goods_sn = props.detailItem?.goods_sn;
//   let id = props.detailItem?.id;
//   console.log('detailId', detailId, obj, index);
//   // deleteOneEdit(detailId,index);
// }
const deleteAll = async () => {
    Modal.confirm({
      title: '确定删除标注内容吗?',
      icon: createVNode(ExclamationCircleOutlined),
      async onOk() {
        let goods_sn = props.detailItem?.goods_sn;
        let id = props.detailItem?.id;
         await deleteEdit({goods_sn, id});
         handleClose();
      },
      onCancel() {
        console.log('Cancel');
      },
      class: 'test',
    });
}
// 追加编辑发送
const sendMsg = (item: any) => {
  if(!uploadList.value.length && !textDesc.value) return message.error("请填写备注或上传图片")
   let params = {
     ...item,
     goods_sn: props.detailItem?.goods_sn,
     content: textDesc.value,
     uploadList: uploadList.value
   }
   sendAddEdit(params);
   textDesc.value = '';
   uploadList.value = [];
}
// 标记解决
const tagAll = async () => {
   Modal.confirm({
      title: '确定该标注内容已解决嘛?',
      icon: createVNode(ExclamationCircleOutlined),
      async onOk() {
        console.log('OK', props.detailItem);
         await toTagAllEdit({goods_sn: props.detailItem?.goods_sn, id: props.detailItem?.id});
         handleClose();
      },
      onCancel() {
        console.log('Cancel');
      },
      class: 'test',
    });
}
const handleUploadChange = async(info: any) => {
  // 根据上传状态设置loading
  if (info.file.status === 'uploading') {
    loading.value = true;
    return;
  }
  
  // 上传完成或失败时设置loading为false
  loading.value = false;
  
  console.log(info);
  let res = info.file.response

  if (res?.status != 1) return;
  message.success("图片上传成功");
  let path = res.data?.image_path;
  let url = res.data?.image_url;
  let name = res.data?.image_name;
  let bits = res.data?.bits;
  let size = res.data?.size;
  let updated_at = res.data?.updated_at;
  
  // 限制最多3张图片，重复插入时覆盖
  if (uploadList.value.length >= 3) {
    // 如果已经有3张图片，替换最后一张
    uploadList.value[uploadList.value.length - 1] = { path: url, name, bits, size, updated_at };
  } else {
    // 如果少于3张，直接添加
    uploadList.value.push({ path: url, name, bits, size, updated_at});
  }
};
// 图片预览相关函数
const openImagePreview = (images: any[], imagePath: string) => {
  previewImages.value = images;
  currentImagePath.value = imagePath;
  previewVisible.value = true;
};

const closeImagePreview = () => {
  previewVisible.value = false;
  previewImages.value = [];
  currentImagePath.value = '';
};
</script>

<template>
<div>
  <!-- 图片预览组件 -->
    <CommonViewerjs
      :image-list="previewImages"
      :current-image-path="currentImagePath"
      :visible="previewVisible"
      @close="closeImagePreview"
    />
  <Modal 
    ref="modalRef" 
    :closable="false" 
    :footer="null" 
    :destroyOnClose="true"
    :open="modelValue" 
    wrapClassName="diffDescModal"
    width="600px" 
    :bodyStyle="{ 
      height: '72vh',
      width:'600px',
      overflow: 'auto',
      padding: 0
    }"
  >
    <template #title>
      <div class="differenceDescModalHead" style="width: 100%; cursor: move">
        <div class="title">差异描述</div>
        <div class="opera-btns">
          <DeleteOutlined @click="deleteAll()" />
          <CheckCircleOutlined @click="tagAll()" />
          <CloseOutlined @click="handleClose" />
        </div>
      </div>
    </template>
     <Spin :spinning="modalLoading || loading">
    <div class="material-detail-content">
      <div class="scroll-container">
        <!-- 循环显示所有diff数据 -->
        <div class="material-detail-content-item" v-for="diffItem in diffData" :key="diffItem.key" @click="currentDiffItem = diffItem">
          <div v-if="diffItem?.is_del!=1">
            <div class="common-content">
              <div class="common-content-head">
                <div class="tip">{{ diffItem.operator?.split('')[0] }}</div>
                <div class="tip-name">{{ diffItem.operator }}</div>
                <div class="tip-time">{{ diffItem.time }}</div>
                <div class="tip-edit" @click.stop="deleteDetailItem(diffItem)">
                  <DeleteOutlined />
                </div>
              </div>
              <div class="common-content-desc" :title="diffItem.content">{{ diffItem.content }}</div>
            </div>
            
            <!-- 上传的图片 -->
            <div class="content-img-list">
              <span class="upload-img-tips" v-if="getNewImages(diffItem).length > 0">上传的图片</span>
              <!-- 小于等于4张时显示 -->
              <div class="mb-5">
                <div v-if="getNewImages(diffItem).length < 5" class="less-custom-carousel">
                  <div v-for="carouselItem in getNewImages(diffItem)" :key="carouselItem.path" class="img-item">
                      <div class="img-wrapper" @click="openImagePreview(getNewImages(diffItem), carouselItem.path)">
                        <img :src="carouselItem.path" :alt="carouselItem.name" class="preview-image" />
                      </div>
                  </div>
                </div>
                <!-- 大于4张时显示 -->
                <Carousel 
                  v-else
                  :slidesToShow="4"
                  :dots="false"
                  :arrows="getNewImages(diffItem).length > 4"
                  class="custom-carousel"
                >
                  <template #prevArrow>
                    <div class="custom-slick-arrow custom-slick-prev">
                      <LeftCircleFilled />
                    </div>
                  </template>
                  <template #nextArrow>
                    <div class="custom-slick-arrow custom-slick-next">
                      <RightCircleFilled />
                    </div>
                  </template>
                  <div v-for="carouselItem in getNewImages(diffItem)" :key="carouselItem.path" class="img-item">
                    <div class="img-wrapper" @click="openImagePreview(getNewImages(diffItem), carouselItem.path)">
                      <img :src="carouselItem.path" :alt="carouselItem.name" class="preview-image" />
                    </div>
                  </div>
                </Carousel>
              </div>
              <div class="material-info" v-for="listItem in getType1Data(diffItem)" :key="listItem.field">
                <span>【{{ listItem.field }}】：</span>
                <span>{{ listItem.field_value }}</span>
              </div>
              <div class="material-info" v-for="infoItem in getType3Data(diffItem)" :key="infoItem.field">
                <span>【{{ infoItem.field }}】：</span>
                <span>{{ infoItem.field_value }}</span>
              </div>
            </div> 
            <!-- 勾选的异常图片 -->
            <div class="content-img-list mt-2">
              <!-- 小于等于4张时显示 -->
              <div v-if="getErrorImages(diffItem).length < 5" class="less-selected-img-list">
                  <div v-for="carouselItem in getErrorImages(diffItem)" :key="carouselItem.path" class="img-item">
                    <div class="img-name" :title="carouselItem.name">{{ carouselItem.name }}</div>
                    <div class="img-wrapper" @click="openImagePreview(getErrorImages(diffItem), carouselItem.path)">
                      <img :src="carouselItem.path" :alt="carouselItem.name" class="preview-image" />
                    </div>
                  </div>
              </div>
              <!-- 大于4张时显示 -->
              <Carousel 
                v-else
                :slidesToShow="4"
                :dots="false"
                :arrows="getErrorImages(diffItem).length > 4"
                class="custom-carousel"
              >
                <template #prevArrow>
                  <div class="custom-slick-arrow custom-slick-prev more-Top">
                    <LeftCircleFilled />
                  </div>
                </template>
                <template #nextArrow>
                  <div class="custom-slick-arrow custom-slick-next more-Top">
                    <RightCircleFilled />
                  </div>
                </template>
                <div v-for="carouselItem in getErrorImages(diffItem)" :key="carouselItem.path" class="img-item">
                  <div class="img-name" :title="carouselItem.name">{{ carouselItem.name }}</div>
                  <div class="img-wrapper" @click="openImagePreview(getErrorImages(diffItem), carouselItem.path)">
                    <img :src="carouselItem.path" :alt="carouselItem.name" class="preview-image" />
                  </div>
                </div>
              </Carousel>
            </div>
            </div>
        </div>
        
     </div>
      <div class="execute-form">
        <div class="tip_name">{{ currentDiffItem?.operator?.split('')[0] }}</div>
        <div class="form-con">
          <div class="form-text">
            <Textarea :value="textDesc" placeholder="请勾选异常后详细描述差异内容～" :autoSize="{ minRows: 2, maxRows: 4 }" @input="(e) => textDesc = e.target.value" />
          </div>
          <div class="common-form-img" v-if="uploadList.length" style="margin-left: 20px;">
            <div v-for="item in uploadList" :key="item.path" class="img-item">
                <div class="img-wrapper">
                  <img :src="item.path" :alt="item.name" class="preview-image" />
                </div>
            </div>
          </div>
           <div class="form-btn">
              <Upload
                :maxCount="3"
                :multiple="true"
                :action="uploadUrl"
                :showUploadList="false"
                :headers="{Token: authStore.token, 'X-CSRF-TOKEN': authStore.token}"
                :data="{el_id: 'img_' + 1,  _token:authStore.token}"
                class="block w-full"
                @change="handleUploadChange"
              >
                <i class="font_family icon-image-error img_icon"></i>
              </Upload>
              <div class="send_icon" @click="sendMsg(currentDiffItem)"><ArrowUpOutlined /></div>
           </div>
        </div>
      </div>
    </div>
    </Spin>
  </Modal>
  </div>
</template>
<style>
.diffDescModal .ant-modal .ant-modal-content{
  padding: 0 !important;
}
</style>
<style scoped lang="scss">

.differenceDescModalHead {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60px;
  padding: 0 20px;
  border-bottom: 1px solid #E7E7E7;
  .title {
    font-size: 16px;
    font-weight: bold;
  }
  .opera-btns {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 10px;
    span {
      cursor: pointer;
      font-size: 16px;
    }
  }
}
 .less-custom-carousel, .less-selected-img-list{
    display: flex;
    .img-item{
      margin-right: 10px;
      width: 120px !important;
    }
    .img-item:last-child{
      margin-right: 0;
    }
  }
.material-detail-content {
  width: 100%;
  padding: 15px 0 15px 15px;
  box-sizing: border-box;
  background-color: #fff;
  height: calc(70vh);
  display: flex;
  flex-direction: column;
  border-bottom-left-radius: 30px;
  border-bottom-right-radius: 30px;
  .scroll-container {
    flex: 1;
    overflow-y: auto;
    padding-right: 15px;
  }
  
  .material-detail-content-item {
    padding-bottom: 10px;
    margin-bottom: 10px;
    .content-img-list {
      padding-left: 35px;
      .upload-img-tips {
        font-size: 12px;
        color: rgba(0,0,0,0.6);
      }
     
      .custom-carousel {
        :deep(.slick-slide) {
          padding: 0 2px;
        }
        :deep(.slick-list) {
          margin: 0 -2px;
        }
        :deep(.slick-track) {
          display: flex;
          align-items: center;
        }
      }
      .custom-slick-arrow {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        z-index: 1;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        color: rgba(0,0,0,0.2);
        cursor: pointer;
        &:hover {
          background: rgba(0, 0, 0, 0.5);
        }
        .anticon {
          font-size: 24px;
        }
      }
      .custom-slick-arrow.more-Top{
        top: 60%;
      }
      .custom-slick-prev {
        left: 7px;
      }
      .custom-slick-next {
        right: 15px;
      }
      .img-item {
        width: 120px !important;
        .img-name {
          font-size: 12px;
          color: rgba(0,0,0,0.6);
          text-align: center;
          margin-top: 5px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .img-wrapper {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          overflow: hidden;
          border-radius: 4px;
        }
        .preview-image {
          max-width: 100%;
          max-height: 100%;
          width: auto;
          height: auto;
          object-fit: contain;
          cursor: pointer;
        }
      }
    }
    .material-info {
      color: rgba(0,0,0,0.4);
    }
  }
  .common-content{
    .common-content-head {
        display: flex;
        align-items: center;
        gap: 10px;
        .tip {
          width: 32px;
          height: 32px;
          background-color: #9847FF;
          border-radius: 50%;
          color: #fff;
          font-size: 16px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .tip-name {
          font-size: 18px;
          font-weight: 500;
          color: #000;
        }
        .tip-time {
          font-size: 14px;
          color: rgba(0,0,0,0.4);
        }
        .tip-edit {
          justify-self: flex-end;
          margin-left: auto;
          cursor: pointer;
          font-size: 16px;
        }
    }
    .common-content-desc {
        font-size: 16px;
        color: rgba(0,0,0,0.8);
        margin: 10px 0;
        padding-left: 40px;
        text-align: left;
        box-sizing: border-box;
    }
  }
  
 .common-form-img{
    padding: 10px 0;
    display: flex;
    .img-item{
      margin-right: 10px;
      width: 120px !important;
    }
    .img-item:last-child{
      margin-right: 0;
    }
  }
  // 操作框
  .execute-form{
    flex-shrink: 0;
    display: flex;
    gap: 10px;
    padding-top: 20px;
    padding-right: 15px;
    border-top: 1px solid #e7e7e7;
    background-color: #fff;
    .tip_name{
        width: 32px;
        height: 32px;
        background-color: #9847FF;
        border-radius: 50%;
        color: #fff;
        font-size: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .form-con{
      flex: 1;
      border-radius: 12px;
      background-color: #f5f5f5;
      .form-text{
        height: 80px;
        padding: 5px;
        .ant-input{
          background-color: transparent;
          border: none !important;
          padding: 5px 10px;
          font-size: 16px;
          color: rgba(0,0,0,0.8);
        }
        .ant-input:focus,.ant-input-focused{
          box-shadow: none !important;
        }
      }
     
      .form-btn{
        padding: 0 10px;
        height: 64px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-top: 1px solid #e7e7e7;
        .img_icon{
          font-size: 24px;
          cursor: pointer;
        }
        .send_icon{
          cursor: pointer;
          width: 32px;
          height: 32px;
          background-color: #1890FF;
          border-radius: 50%;
          color: #fff;
          font-size: 16px;
          text-align: center;
          line-height: 32px;
        }
      }
    }
  }
}
</style>
