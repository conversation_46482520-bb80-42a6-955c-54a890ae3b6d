<script setup lang="ts">
import { onMounted, ref, nextTick, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { useTemplateStore } from '#/store';
import ModuleDialog from './otherCom/module-dialog.vue';
import ModuleSetDialog from './otherCom/module-set-dialog.vue';
import Sortable from 'sortablejs';
import { attrbureConfigImage } from '#/components/attributeComp/config/image-config';
import type { BoxType } from '#/store/types/template';

const templateStore = useTemplateStore();
const {
  activate_box,
  activate_el,
  boxes,
  elements,
  modules,
  activate_module,
  expandedModules,
  expandedBoxes,
  expandedElements,
} = storeToRefs(templateStore);
const {
  setAllBoxes,
  setActiveModule,
  setActiveBox,
  setActiveElement,
  setAllElements,
  updateExpandedModules,
  updateExpandedBoxes,
  updateExpandedElements,
  delElement,
  copyElement,
  delModule,
  moduleMove,
  controlModule,
  addModuleBox,
  scrollToActiveBox,
} = templateStore;
// 常量定义
const TYPE_IMAGE = 1;
const TYPE_TEXT = 2;
const TYPE_HOT = 'HOT_LINK';
const TYPE_SHAPE = 'SHAPE';

const boxLoopList = {
  0: '默认',
  1: '系列$',
  2: '循环*',
};

const img_type: any = {};
attrbureConfigImage.imgType.config.map((item: any) => {
  img_type[item.value] = item.label;
});

// 状态管理
const moduleVisible = ref(false);
const dialogName = ref('');
const setModuleVisible = ref(false);

// 计算属性：确保模块显示状态的响应性
const getModuleShowState = (module: any) => {
  return Boolean(module.module_show);
};

// 初始化拖拽排序
const initSortable = () => {
  // 图片元素排序
  const imageList = document.querySelector('.image-list');
  if (imageList) {
    new Sortable(imageList, {
      group: 'images',
      animation: 150,
      onEnd: (evt) => handleDragEnd(TYPE_IMAGE, evt),
    });
  }

  // 文本元素排序
  const textList = document.querySelector('.text-list');
  if (textList) {
    new Sortable(textList, {
      group: 'texts',
      animation: 150,
      onEnd: (evt) => handleDragEnd(TYPE_TEXT, evt),
    });
  }
  // 图形元素排序
  const shapeList = document.querySelector('.shape-list');
  if (shapeList) {
    new Sortable(shapeList, {
      group: 'shapes',
      animation: 150,
      onEnd: (evt) => handleDragEnd(TYPE_SHAPE, evt),
    });
  }

  // 热区元素排序
  const hotLinkList = document.querySelector('.hotlink-list');
  if (hotLinkList) {
    new Sortable(hotLinkList, {
      group: 'hotlinks',
      animation: 150,
      onEnd: (evt) => handleDragEnd(TYPE_HOT, evt),
    });
  }
};
// 处理拖拽结束事件
const handleDragEnd = (type: string | number, evt: any) => {
  const { oldIndex, newIndex } = evt;
  if (oldIndex === newIndex || !activate_box.value) return;

  const box = boxes.value[activate_box.value.key] as BoxType;
  const isHotLink = type === TYPE_HOT;
  const targetArray = isHotLink ? box.hotLinkList : box.content;

  // 获取拖拽后的新顺序
  const newOrder = [...targetArray];
  const [movedItem] = newOrder.splice(oldIndex, 1);
  newOrder.splice(newIndex, 0, movedItem);

  // 更新画布中的元素顺序
  if (isHotLink) {
    box.hotLinkList = newOrder;
  } else {
    box.content = newOrder;
  }
  // 更新 elements 中键值对的顺序
  const newElements = {};

  // 1. 首先添加当前画布中重新排序的元素
  newOrder.forEach((key) => {
    newElements[key] = elements.value[key];
  });

  // 2. 然后添加其他画布中的元素
  Object.keys(elements.value).forEach((key) => {
    // 如果这个元素不在当前画布的新顺序中，则添加到新对象中
    if (!newOrder.includes(key)) {
      newElements[key] = elements.value[key];
    }
  });

  // 更新全局状态
  setAllElements(newElements);
  // 更新全局状态
  setAllBoxes(boxes.value);
};
// 计算属性
const computedBoxName = (box: any) => {
  if (!box) return '';
  const name = box.name || '画布';
  const loopType = boxLoopList[box.formula_name_type] || '';
  let loopNum = '';

  if (box.formula_name_type == 1) {
    loopNum = box.formula_series_num;
  } else if (box.formula_name_type == 2) {
    loopNum = box.formula_loop_num;
  }

  return box.formula_name_type === '0'
    ? name
    : `${name}-${loopType}${loopNum || 0}`;
};

// 切换模块展开状态
const toggleModule = (moduleKey: string) => {
  let expanded = expandedModules.value[moduleKey];
  updateExpandedModules(moduleKey, !expanded);
};

// 切换画布展开状态
const toggleBox = (boxKey: string) => {
  updateExpandedBoxes(boxKey, !expandedBoxes.value[boxKey]);
};

// 切换元素类型展开状态
const toggleElementType = (boxKey: string, type: string) => {
  const key = `${boxKey}-${type}`;
  updateExpandedElements(key, !expandedElements.value[key]);
};

// 激活模块
const activeModule = (module: ModuleType) => {
  if (!module.module_show) return;
  setActiveModule(module);
  let firstBoxKey = module.boxes[0];
  if (firstBoxKey) {
    setActiveBox({ key: firstBoxKey, ...boxes.value[firstBoxKey] });
    scrollToActiveBox();
  }

  // 如果模块是显示的，则展开
  if (module.module_show) {
    updateExpandedModules(module.module_key, true);
    updateExpandedBoxes(firstBoxKey, true);
  }
};

// 模块编辑
const editModule = (moKey: string, module: any) => {
  moduleVisible.value = true;
  dialogName.value = '模块编辑';
};
// 模块添加
const handleAddModule = () => {
  moduleVisible.value = true;
  dialogName.value = '模块新增';
};
// 模块设置
const settingModule = (moKey: string, module: any) => {
  setModuleVisible.value = true;
};
// 模块复制
const copyModule = (moKey: string, module: any) => {
  moduleVisible.value = true;
  dialogName.value = '模块复制';
};
// 监听激活画布变化，重新初始化排序
watch(
  () => activate_box.value,
  () => {
    nextTick(() => {
      initSortable();
    });
  },
);
onMounted(() => {
  initSortable();
});
</script>

<template>
  <div
    class="flex h-full w-[320px] flex-col border-r border-gray-200 bg-white text-black"
  >
    <div class="box-sizing border-box color-[#000] h-[35px] bg-[#f0f0f0] text-center text-xs font-bold leading-[35px]">
      模块列表
    </div>
    <div class="relative h-[calc(100vh-210px)]">
      <div class="module_box h-[calc(100%-130px)] overflow-y-auto">
        <div
          v-for="(module, index) in modules"
          :key="module.module_key"
          class="module_box_item"
          :module-key="module.module_key"
        >
          <div
            class="module_item flex items-center border-b border-gray-200 p-2"
            :style="{
              background:
                module.module_key === activate_module.module_key
                  ? 'rgb(226 243 255)'
                  : '',
            }"
            data-type="0"
          >
            <i
              v-if="getModuleShowState(module)"
              class="font_family icon-dakai editIcon mr-1 cursor-pointer"
              @click="controlModule(module.module_key, false)"
              title="隐藏模块"
            ></i>
            <i
              v-else
              class="font_family icon-browse-off editIcon mr-1 cursor-pointer"
              @click="controlModule(module.module_key, true)"
              title="显示模块"
            ></i>
            <i
              class="font_family icon-xiala12 m_pack module_menu_icon mr-1"
              :class="{ pack_change: !expandedModules[module.module_key] }"
              @click="toggleModule(module.module_key)"
            ></i>
            <span class="module_m1_i w-[18px]">{{ index + 1 }}</span>
            <span
              class="pointer flex-1 cursor-pointer overflow-hidden text-ellipsis whitespace-nowrap"
              @click="activeModule(module)"
            >
              {{ module.name || '模块' }} ({{ module.boxes.length }})
            </span>

            <!-- 模块操作按钮 -->
            <span
              v-if="
                (module.module_key === activate_module.module_key &&
                  getModuleShowState(module) &&
                  module.boxes.includes(activate_box?.id || '')) ||
                module.module_key === activate_module?.module_key
              "
              class="flex items-center"
            >
              <img
                v-if="index !== 0"
                class="mr-1 h-3 w-3 cursor-pointer"
                src="/image/icon/arrow-up.png"
                alt="上移"
                title="上移"
                @click="moduleMove(module.module_key, 'up')"
              />
              <img
                v-if="index !== modules.length - 1"
                class="mr-1 h-3 w-3 cursor-pointer"
                src="/image/icon/arrow-down.png"
                alt="下移"
                title="下移"
                @click="moduleMove(module.module_key, 'down')"
              />
              <i
                class="font_family icon-icon-15 editIcon mr-1 cursor-pointer"
                title="模块设置"
                @click="settingModule(module.module_key, module)"
              ></i>
              <i
                class="font_family icon-bianji1 editIcon mr-1 cursor-pointer"
                title="模块编辑"
                @click="editModule(module.module_key, module)"
              ></i>
              <i
                class="font_family icon-fuzhi editIcon mr-1 cursor-pointer"
                title="模块复制"
                @click="copyModule(module.module_key, module)"
              ></i>
              <i
                class="font_family icon-shanchu1 editIcon mr-1 cursor-pointer"
                title="模块删除"
                @click="delModule(module.module_key)"
              ></i>
              <i
                class="font_family icon-library_add editIcon cursor-pointer"
                title="新增画布"
                @click="addModuleBox(module.module_key)"
              ></i>
            </span>
          </div>

          <!-- 画布列表 -->
          <div
            v-if="expandedModules[module.module_key] && getModuleShowState(module)"
            class="bg-opacity-40"
            :style="{
              background:
                module.boxes.includes(activate_box?.id || '') &&
                module.module_key === activate_module.module_key
                  ? 'rgb(226 243 255 / 43%)'
                  : '',
            }"
          >
            <div
              v-for="(boxKey, boxIndex) in module.boxes"
              :key="boxKey"
              :box-id="boxKey"
            >
              <!-- 画布内容 -->
              <div
                class="module_item module_m1 ele flex items-center border-b border-gray-200 p-2"
                data-type="1"
                :data-id="boxKey"
                :module-key="module.module_key"
              >
                <i
                  class="font_family icon-xianshi m_view mr-5"
                  style="opacity: 0"
                ></i>
                <i
                  class="font_family icon-xiala12 m_pack box_menu_icon mr-2"
                  :class="{ pack_change: !expandedBoxes[boxKey] }"
                  @click="toggleBox(boxKey)"
                ></i>
                <span class="module_m1_i mr-2">{{ boxIndex + 1 }}.</span>
                <span
                  class="pointer cursor-pointer"
                  @click="setActiveBox(boxes[boxKey], module)"
                >
                  {{ computedBoxName(boxes[boxKey]) }}
                </span>
              </div>
              <!-- 元素类型列表 -->
              <div v-if="expandedBoxes[boxKey] && boxes[boxKey]?.box_show">
                <!-- 图片元素 -->
                <div
                  class="module_item module_m2 no_r_btn ele flex items-center border-b border-gray-200 p-2 pl-4"
                  data-nType="img"
                  data-type="2"
                  :box-el-id="boxKey"
                  :module-key="module.module_key"
                >
                  <i
                    class="font_family icon-xianshi m_view mr-5"
                    style="opacity: 0"
                  ></i>
                  <i
                    class="font_family icon-xiala12 m_pack el_menu_icon mr-2"
                    :class="{ pack_change: !expandedElements[`${boxKey}-img`] }"
                    @click="toggleElementType(boxKey, 'img')"
                  ></i>
                  <span
                    class="cursor-pointer pr-2"
                    @click="toggleElementType(boxKey, 'img')"
                    >图片</span
                  >
                  <i class="font_family icon-a-tupian1 img text-[#1890ff]"></i>
                </div>
                <div
                  v-if="expandedElements[`${boxKey}-img`]"
                  data-type="3"
                  box-opera-type="img"
                  class="module_m3 element ele image-list"
                  :id="`${boxKey}-img`"
                  :box-id="boxKey"
                  :module-key="module.module_key"
                >
                  <div v-for="elKey in boxes[boxKey]?.content" :key="elKey">
                    <div
                      v-if="elements[elKey]?.type == TYPE_IMAGE"
                      data-type="DRAG_COPY"
                      class="module-m3-item z-[6] cursor-move border-b border-[#e3e4e5]"
                      :class="{
                        this_move_box: elKey == activate_el?.element_id,
                      }"
                      :data-id="boxKey"
                      :data-pid="elKey"
                      @click="
                        () => {
                          setActiveElement(elements[elKey], boxKey, module);
                          setActiveBox(boxes[boxKey], module);
                        }
                      "
                    >
                      <div
                        class="module-m3-title flex w-full items-center overflow-hidden text-ellipsis whitespace-nowrap p-2 pl-[75px] text-left"
                        :title="elements[elKey]?.name"
                      >
                        <div
                          class="module-m3-info w-[152px] overflow-hidden text-ellipsis whitespace-nowrap"
                        >
                          {{ img_type[`${elements[elKey].imgType}`] }} -
                          {{ elements[elKey].name }}
                        </div>
                        <div class="flex items-center">
                          <i
                            class="font_family icon-fuzhi module-m3-del mx-1 cursor-pointer"
                            title="复制"
                            @click="copyElement('img', elements[elKey])"
                          ></i>
                          <i
                            class="font_family icon-shanchu1 module-m3-del cursor-pointer"
                            title="删除"
                            @click.stop="delElement(boxKey, elKey)"
                          ></i>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 文本元素 -->
                <div
                  class="module_item module_m2 no_r_btn ele flex items-center border-b border-gray-200 p-2 pl-4"
                  data-nType="text"
                  data-type="2"
                  :box-el-id="boxKey"
                >
                  <i
                    class="font_family icon-xianshi m_view mr-5"
                    style="opacity: 0"
                  ></i>
                  <i
                    class="font_family icon-xiala12 m_pack el_menu_icon mr-2"
                    :class="{
                      pack_change: !expandedElements[`${boxKey}-text`],
                    }"
                    @click="toggleElementType(boxKey, 'text')"
                  ></i>
                  <span
                    class="cursor-pointer pr-2"
                    @click="toggleElementType(boxKey, 'text')"
                    >文本</span
                  >
                  <i class="font_family icon-a-lianhe2 text text-[#f65e49]"></i>
                </div>
                <div
                  v-if="expandedElements[`${boxKey}-text`]"
                  data-type="3"
                  box-opera-type="text"
                  class="module_m3 element ele text-list"
                  :id="`${boxKey}-text`"
                  :box-id="boxKey"
                  :module-key="module.module_key"
                >
                  <div v-for="elKey in boxes[boxKey]?.content" :key="elKey">
                    <div
                      v-if="elements[elKey]?.type == TYPE_TEXT"
                      class="module-m3-item z-[6] cursor-move border-b border-[#e3e4e5]"
                      :class="{
                        this_move_box: elKey == activate_el?.element_id,
                      }"
                      data-type="DRAG_COPY"
                      :data-id="boxKey"
                      :data-pid="elKey"
                      @click="
                        () => {
                          setActiveElement(elements[elKey], boxKey, module);
                          setActiveBox(boxes[boxKey], module);
                        }
                      "
                    >
                      <div
                        class="module-m3-title flex w-full items-center overflow-hidden text-ellipsis whitespace-nowrap p-2 pl-[75px] text-left"
                      >
                        <span
                          class="module-m3-info w-[152px] overflow-hidden text-ellipsis whitespace-nowrap"
                          >{{ elements[elKey]?.name }}</span
                        >
                        <div class="flex items-center">
                          <i
                            class="font_family icon-fuzhi module-m3-del mx-1 cursor-pointer"
                            title="复制"
                            @click="copyElement('text', elements[elKey])"
                          ></i>
                          <i
                            class="font_family icon-shanchu1 module-m3-del cursor-pointer"
                            title="删除"
                            @click.stop="delElement(boxKey, elKey)"
                          ></i>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 热区元素 -->
                <div
                  class="module_item module_m2 no_r_btn ele flex items-center border-b border-gray-200 p-2 pl-4"
                  data-nType="hotspot"
                  data-type="2"
                  :box-el-id="boxKey"
                >
                  <i
                    class="font_family icon-xianshi m_view mr-5"
                    style="opacity: 0"
                  ></i>
                  <i
                    class="font_family icon-xiala12 m_pack el_menu_icon mr-2"
                    :class="{
                      pack_change: !expandedElements[`${boxKey}-hotspot`],
                    }"
                    @click="toggleElementType(boxKey, 'hotspot')"
                  ></i>
                  <span
                    class="cursor-pointer pr-2"
                    @click="toggleElementType(boxKey, 'hotspot')"
                    >热区</span
                  >
                  <i class="font_family icon-a-requ2 hot text-[#ff8d34]"></i>
                </div>
                <div
                  v-if="expandedElements[`${boxKey}-hotspot`]"
                  data-type="3"
                  box-opera-type="hotspot"
                  class="module_m3 element ele hotlink-list"
                  :id="`${boxKey}-hotspot`"
                  :box-id="boxKey"
                  :module-key="module.module_key"
                >
                  <div v-for="elKey in boxes[boxKey]?.hotLinkList" :key="elKey">
                    <div
                      v-if="elements[elKey]?.type == TYPE_HOT"
                      class="module-m3-item z-[6] cursor-move border-b border-[#e3e4e5]"
                      :class="{
                        this_move_box: elKey == activate_el?.element_id,
                      }"
                      data-type="DRAG_COPY"
                      :data-id="boxKey"
                      :data-pid="elKey"
                      @click="setActiveElement(elements[elKey], boxKey, module)"
                    >
                      <div
                        class="module-m3-title flex w-full items-center overflow-hidden text-ellipsis whitespace-nowrap p-2 pl-[75px] text-left"
                      >
                        <span
                          class="module-m3-info w-[152px] overflow-hidden text-ellipsis whitespace-nowrap"
                          >{{ elements[elKey]?.name }}</span
                        >
                        <div class="flex items-center">
                          <i
                            class="font_family icon-fuzhi module-m3-del mx-1 cursor-pointer"
                            title="复制"
                            @click="copyElement('hotspot', elements[elKey])"
                          ></i>
                          <i
                            class="font_family icon-shanchu1 module-m3-del cursor-pointer"
                            title="删除"
                            @click.stop="delElement(boxKey, elKey)"
                          ></i>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 图形元素 -->
                <div
                  class="module_item module_m2 no_r_btn ele flex items-center border-b border-gray-200 p-2 pl-4"
                  data-nType="arrow"
                  data-type="2"
                  data-pId="1"
                  :box-el-id="boxKey"
                >
                  <i
                    class="font_family icon-xianshi m_view mr-5"
                    style="opacity: 0"
                  ></i>
                  <i
                    class="font_family icon-xiala12 m_pack el_menu_icon mr-2"
                    :class="{
                      pack_change: !expandedElements[`${boxKey}-arrow`],
                    }"
                    @click="toggleElementType(boxKey, 'arrow')"
                  ></i>
                  <span
                    class="cursor-pointer pr-2"
                    @click="toggleElementType(boxKey, 'arrow')"
                    >图形</span
                  >
                  <i class="font_family icon-tuxing text text-[#46d43b]"></i>
                </div>
                <div
                  v-if="expandedElements[`${boxKey}-arrow`]"
                  data-type="3"
                  box-opera-type="arrow"
                  class="module_m3 element ele shape-list"
                  :id="`${boxKey}-arrow`"
                  :box-id="boxKey"
                  :module-key="module.module_key"
                >
                  <div v-for="elKey in boxes[boxKey]?.content" :key="elKey">
                    <div
                      v-if="elements[elKey]?.type == TYPE_SHAPE"
                      class="module-m3-item z-[6] cursor-move border-b border-[#e3e4e5]"
                      :class="{
                        this_move_box: elKey == activate_el?.element_id,
                      }"
                      data-type="DRAG_COPY"
                      :data-id="boxKey"
                      :data-pid="elKey"
                      @click="setActiveElement(elements[elKey], boxKey, module)"
                    >
                      <div
                        class="module-m3-title flex w-full items-center overflow-hidden text-ellipsis whitespace-nowrap p-2 pl-[75px] text-left"
                      >
                        <span
                          class="module-m3-info w-[152px] overflow-hidden text-ellipsis whitespace-nowrap"
                          >{{ elements[elKey]?.name }}</span
                        >
                        <div class="flex items-center">
                          <i
                            class="font_family icon-fuzhi module-m3-del mx-1 cursor-pointer"
                            title="复制"
                            @click="copyElement('text', elements[elKey])"
                          ></i>
                          <i
                            class="font_family icon-shanchu1 module-m3-del cursor-pointer"
                            title="删除"
                            @click.stop="delElement(boxKey, elKey)"
                          ></i>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div
        class="module_add absolute  left-0 z-10 h-[35px] w-full cursor-pointer border border-gray-200 bg-blue-500 text-center text-sm leading-[35px] text-white"
        @click="handleAddModule"
      >
        添加模块
      </div>
    </div>
    <ModuleDialog
      :visible="moduleVisible"
      :dialogName="dialogName"
      :close="() => (moduleVisible = false)"
    />
    <ModuleSetDialog
      :visible="setModuleVisible"
      :close="() => (setModuleVisible = false)"
    />
  </div>
</template>

<style scoped>
/* 仅保留特殊样式，其他使用 Tailwind 类 */
.pack_change {
  @apply rotate-[-90deg] transform;
}

.module_box_item .pointer {
  @apply cursor-pointer;
}

.this_move_box {
  @apply z-[11] border border-dashed border-blue-500 leading-[1];
}

/* 其他必要的自定义样式... */
</style>
