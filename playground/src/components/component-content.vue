<script lang="ts" setup>
import { ref, onBeforeUnmount, computed, watch, onMounted } from 'vue';
import { Vue3RulerTool } from 'vue3-ruler-tool';
import 'vue3-ruler-tool/dist/style.css';
import { useAppConfig } from '@vben/hooks';

import { useTemplateStore } from '#/store';
import { storeToRefs } from 'pinia';
import { message } from 'ant-design-vue';
import BoxRemarks from './otherCom/box-remarks.vue';
import ClipboardJS from 'clipboard';
import {
  countTargetTop,
  countTargetLeft,
  copyBoxClipboard,
  countTargetLink,
} from './attributeComp/commonFuc';
import { imageProcessor } from './attributeComp/imageCropper';
import { shapeProcessor } from './attributeComp/shapeCropper';
import type { ElementType, BoxType } from '#/store/types/template';
import defaultImg from '/image/icon/thumbnail-new.png';

interface DragState {
  direction?: string;
  ori_left?: number;
  ori_top?: number;
  ori_page_y?: number;
  ori_page_x?: number;
  rollBack?: () => void;
  eDragFun?: (event: MouseEvent) => void;
  dDragFun?: (event: MouseEvent) => void;
  event?: HTMLElement;
  ori_height?: number;
  ori_width?: number;
}

interface RulerOptions {
  unit: 'px' | 'mm' | 'cm' | 'in';
  tickMajor: number;
  tickMinor: number;
  tickMicro: number;
  showLabel: boolean;
  arrowStyle: 'arrow' | 'line' | 'none';
  startX: number;
  startY: number;
}

// Constants
const RULER_LEFT = 100;
const RULER_TOP = 100;
const DRAG_EL_MOVE = 'EL_MOVE';
const DRAG_EL_CHANGE_SIZE = 'EL_CHANGE_SIZE';
const DRAG_EL_CHANGE_ROTATE = 'EL_CHANGE_ROTATE';
const DRAG_BOX_HEIGHT = 'BOX_HEIGHT';
const TYPE_IMAGE = '1';
const TYPE_TEXT = '2';
const TYPE_HOT = 'HOT_LINK';
const TYPE_SHAPE = 'SHAPE';
const VERSION = '0.2';
//元件库拖拽类型
const DRAG_IMAGE = 1;
const DRAG_TEXT = 2;
const DRAG_HOT = 'HOT_LINK';
const DRAG_COPY = 'DRAG_COPY';

const templateStore = useTemplateStore();
const {
  activate_box,
  activate_el,
  boxes,
  elements,
  boxesKeys,
  modules,
  shapeImgConfig,
} = storeToRefs(templateStore);
const {
  setAllBoxes,
  setAllElements,
  setActiveBox,
  addBox,
  delBox,
  shapeImg,
  delElement,
  moveBox,
  scrollToActiveBox,
  addElement,
  setExpandedElement,
} = templateStore;
// Local state
const s_drag_t = ref<number | null>(null);
const drag = ref<DragState>({});
const dDragFun = ref<(event: MouseEvent) => void>(() => {});
const eDragFun = ref<(event: MouseEvent) => void>(() => {});
const remarksVisible = ref(false);
// 元素旋转相关变量
const isRotating = ref(false);
const mouseBeginX = ref(0);
const mouseBeginY = ref(0);
const centerX = ref(0);
const centerY = ref(0);
const originRotate = ref(0);
const rotationFrame = ref<number | null>(null);
const currentRotation = ref<number>(0);

// 修改图片处理相关的代码
const processedImages = new Set<string>();
const processedShapes = new Set<string>();

// 在组件卸载时清理
onBeforeUnmount(() => {
  const elementToRemove = document.getElementById('copyBoxHtml');
  if (elementToRemove) {
    elementToRemove.parentNode?.removeChild(elementToRemove);
  }

  processedImages.clear();
  processedShapes.clear();
  imageProcessor.clearImageCache();
  shapeProcessor.clearProcessState();
});
const getElementStyle = computed(() => {
  return (el: ElementType) => {
    if (!el) return {};

    const style: Record<string, string> = {
      width: `${el.type == 1 ? el.imgWidth : el.width}px`,
      height:
        el.type == TYPE_TEXT
          ? 'auto'
          : `${el.type == 1 ? el.imgHeight : el.height}px`,
      left: `${countTargetLeft(el)}px`,
      top: `${countTargetTop(el)}px`,
      zIndex: el.zIndex ? `${999 - parseInt(el.zIndex)}` : '998',
    };
    if (el.type == TYPE_TEXT) {
      Object.keys(el).forEach((elKeyItem) => {
        if (typeof el[elKeyItem] === 'string') {
          style[elKeyItem] = el[elKeyItem] as string;
        }
      });
      if (el.lineHeight) {
        style.lineHeight = `${el.lineHeight}px`;
      }
      if (el.width) {
        style.width = `${el.width}px`;
      }
      if (el.fontSize) style.fontSize = `${el.fontSize}px`;
      if (el.wordBreak) style.wordBreak = 'break-all';
      if (el.zIndex) style.zIndex = `${999 - parseInt(el.zIndex)}`;
      // console.log('33333',style);
    } else if (el.type == TYPE_IMAGE) {
      if (el.bg_color) {
        style.backgroundColor = el.bg_color;
      } else if (style.memoryImgPath) {
        style.backgroundColor = 'transparent';
      }
      if (
        (el.bg_img_gradation_config1 && el.bg_img_gradation_config1 != '0') ||
        (el.bg_img_gradation_config2 && el.bg_img_gradation_config2 != '0') ||
        (el.bg_img_gradation_config3 && el.bg_img_gradation_config3 != '0')
      ) {
        let color1 = el.bg_img_gradation_color1
          ? el.bg_img_gradation_color1
          : '#fff';
        let color2 = el.bg_img_gradation_color2
          ? el.bg_img_gradation_color2
          : '#fff';
        let color3 = el.bg_img_gradation_color3
          ? el.bg_img_gradation_color3
          : '#fff';
        let color_percent1 = el.bg_img_gradation_config1 || 0;
        let color_percent2 = el.bg_img_gradation_config2 || 0;
        let color_percent3 = el.bg_img_gradation_config3 || 0;
        style.backgroundImage = `linear-gradient(to bottom, ${color1} ${color_percent1}%, ${color2} ${color_percent2}%, ${color3}  ${color_percent3 || 100}%)`;
      }
    } else if (el.type == TYPE_SHAPE) {
      setShapeBorderColor.call(this, { ...style, ...el });
      if (Number(el.shapeType) == 6) {
        style.fillColor = el.fill_color;
      }
    } else if (el.type == TYPE_HOT) {
      const linkStyle = countTargetLink(el);
      if (linkStyle) {
        Object.assign(style, linkStyle);
      }
      // console.log('style', style, linkStyle);
    }
    if (el.rotate) {
      style.transform = `rotate(${el.rotate}deg)`;
      style.transformOrigin = 'center center';
    }
    return style;
  };
});

const setShapeBorderColor = (data: Record<string, string>) => {
  let shapeId = `arrow${data?.element_id}`;

  let newColor = data.bor_color || '#000';
  let image = new Image();
  let defaultImg = shapeImgConfig.value[1];
  let type = +data.shapeType;
  if (shapeImgConfig.value[type]) {
    defaultImg = shapeImgConfig.value[type];
  }
  image.onerror = (error) => {
    console.error('图片加载失败:', error);
    console.error('失败的图片路径:', defaultImg);
  };
  //设置图片加载完成后的回调函数
  image.onload = () => {
    let imgElement = document.getElementById(shapeId) as any;
    if (imgElement) {
      let svgDoc = imgElement.children[0];
      if (svgDoc) {
        let pathElements = svgDoc.querySelectorAll('path');
        for (const pathItem of pathElements) {
          pathItem?.setAttribute('fill', newColor);
        }
        let rectElements = svgDoc.querySelectorAll('rect');
        for (const rectItem of rectElements) {
          rectItem?.setAttribute('fill', newColor);
        }
        let lineElement = svgDoc.querySelectorAll('line');
        for (const lineItem of lineElement) {
          lineItem?.setAttribute('stroke', newColor);
        }
      }
    }
  };
  // 设置图片源
  try {
    image.src = defaultImg;
  } catch (error) {
    console.error('设置图片源时出错:', error);
  }
};
// 画布样式

const boxStyleObject = (box: BoxType) => {
  return {
    width: `${box?.width}px`,
    height: `${box?.height}px`,
    backgroundColor: box?.bg_color || '#fff',
  };
};

const findModule = (boxKey: string) => {
  let targetModule = modules.value.find((item) => item.boxes.includes(boxKey));
  return targetModule;
};
const sDrag = (
  type: string,
  event: MouseEvent,
  element: ElementType,
  box: string,
) => {
  if (event) {
    event.preventDefault();
    event.stopPropagation();
  }
  if (!activate_box.value) return;
  let targetModule = findModule(activate_box.value.key);
  if (element) {
    activate_el.value = { key: element.element_id, ...element };
		console.log('----sDrag----activate_el.value', activate_el.value);
    setExpandedElement(box, element.type);
  }
  s_drag_t.value = new Date().valueOf();
  drag.value = {};
  drag.value.direction = type;
  const parentNode = (event.currentTarget as HTMLElement)
    .parentNode as HTMLElement;
  drag.value.ori_left = parentNode?.offsetLeft || 0;
  drag.value.ori_top = parentNode?.offsetTop || 0;
  drag.value.ori_page_y = event.pageY;
  drag.value.ori_page_x = event.pageX;

  // 保存当前拖拽元素的引用
  const currentDragElement = parentNode;

  switch (type) {
    case DRAG_BOX_HEIGHT:
      //box高度变更
      drag.value.event = currentDragElement;
      drag.value.ori_height = currentDragElement.offsetHeight;
      drag.value.rollBack = function () {
        currentDragElement.style.height = `${drag.value.ori_left}px`;
        currentDragElement.style.imgHeight = `${drag.value.ori_left}px`;
      };
      eDragFun.value = (event) => {
        if (currentDragElement && activate_el.value) {
          let height = currentDragElement.offsetHeight;
          setActiveBox({ ...activate_box.value, height, imgHeight: height });
        }
      };
      dDragFun.value = (event) => {
        let height = parseInt(
          drag.value.ori_height + (event.pageY - drag.value.ori_page_y),
        );
        if (height <= 50) {
          height = 50;
        }
        currentDragElement.style.height = `${height}px`;
        currentDragElement.style.imgHeight = `${height}px`;
      };
      break;
    case DRAG_EL_MOVE:
      drag.value.event = currentDragElement;
      drag.value.ori_height = currentDragElement.offsetHeight - 2;
      drag.value.ori_width = currentDragElement.offsetWidth - 2;
      const box_event = currentDragElement.parentNode
        ?.parentNode as HTMLElement;

      drag.value.rollBack = () => {
        if (currentDragElement) {
          currentDragElement.style.left = `${drag.value.ori_left}px`;
          currentDragElement.style.top = `${drag.value.ori_top}px`;
        }
      };

      eDragFun.value = () => {
        if (currentDragElement && activate_el.value) {
          let x = currentDragElement.offsetLeft;
          let y = currentDragElement.offsetTop;
          activate_el.value = { ...activate_el.value, x, y };
					console.log('----eDragFun----activate_el.value', activate_el.value);
					
        }
      };

      dDragFun.value = (event) => {
        if (currentDragElement && box_event) {
          const box_height = box_event.offsetHeight;
          const box_width = box_event.offsetWidth;
          const max_top = parseInt(`${box_height - drag.value.ori_height}`);
          const max_left = parseInt(`${box_width - drag.value.ori_width}`);
          const offsetY = event.pageY - drag.value.ori_page_y;
          const offsetX = event.pageX - drag.value.ori_page_x;
          const rotate = Number(activate_el.value.rotate || 0);

          let left = parseInt(`${drag.value.ori_left! + offsetX}`);
          let top = parseInt(`${drag.value.ori_top + offsetY}`);
          if (rotate % 180 == 0) {
            if (top < 0) top = 0;
            if (top > max_top) top = max_top;
            if (left < 0) left = 0;
            if (left > max_left) left = max_left;
          }
          currentDragElement.style.left = `${left}px`;
          currentDragElement.style.top = `${top}px`;
        }
      };
      break;

    case DRAG_EL_CHANGE_SIZE:
      drag.value.event = currentDragElement;
      drag.value.ori_height = currentDragElement.offsetHeight - 2;
      drag.value.ori_width = currentDragElement.offsetWidth - 2;
      const box_event2 = currentDragElement.parentNode
        ?.parentNode as HTMLElement;

      drag.value.rollBack = () => {
        if (currentDragElement) {
          currentDragElement.style.height = `${drag.value.ori_height}px`;
          currentDragElement.style.width = `${drag.value.ori_width}px`;
        }
      };

      eDragFun.value = () => {
        if (currentDragElement && activate_el.value) {
          let height = currentDragElement.offsetHeight;
          let width = currentDragElement.offsetWidth;
          activate_el.value = {
            ...activate_el.value,
            height,
            width,
            imgHeight: height,
            imgWidth: width,
          };
        }
      };

      dDragFun.value = (event) => {
        if (currentDragElement && box_event2) {
          const offsetY = event.pageY - drag.value.ori_page_y!;
          const offsetX = event.pageX - drag.value.ori_page_x!;
          const box_height = box_event2.offsetHeight;
          const box_width = box_event2.offsetWidth;

          let width = parseInt(`${drag.value.ori_width! + offsetX}`);
          let height = parseInt(`${drag.value.ori_height! + offsetY}`);
          if (activate_el.value.type !== 'SHAPE') {
            if (width < 50) width = 50;
            if (height < 50) height = 50;
          }

          if (parseInt(`${width + drag.value.ori_left!}`) > box_width) {
            width = parseInt(`${box_width - drag.value.ori_left!}`);
          }
          if (parseInt(`${height + drag.value.ori_top!}`) > box_height) {
            height = parseInt(`${box_height - drag.value.ori_top!}`);
          }

          currentDragElement.style.height = `${height - 2}px`;
          currentDragElement.style.width = `${width - 2}px`;
          currentDragElement.style.imgHeight = `${height - 2}px`;
          currentDragElement.style.imgWidth = `${width - 2}px`;
        }
      };
      break;

    case DRAG_EL_CHANGE_ROTATE:
      // 元素旋转
      drag.value.event = currentDragElement;

      // // 回滚函数
      // drag.value.rollBack = () => {
      //   if (currentDragElement && activate_el.value?.rotate) {
      //     currentDragElement.style.transform = `rotate(${activate_el.value.rotate}deg)`;
      //   }
      // };
      // 修改回滚函数
      drag.value.rollBack = () => {
        if (currentDragElement && activate_el.value?.rotate) {
          currentDragElement.style.transform = `rotate(${activate_el.value.rotate}deg)`;
          currentDragElement.style.willChange = 'auto';
        }
      };
      // 初始化旋转状态
      isRotating.value = true;
      [mouseBeginX.value, mouseBeginY.value] = [event.clientX, event.clientY];
      originRotate.value = activate_el.value?.rotate || 0;
      currentRotation.value = originRotate.value;

      const boundingClient = document
        .getElementById(activate_el.value?.element_id)
        ?.getBoundingClientRect();
      let rotateLeft = boundingClient?.left || 0;
      let rotateTop = boundingClient?.top || 0;
      // 根据元素类型确定旋转参考尺寸
      let rotateHeight = 0;
      let rotateWidth = 0;

      if (activate_el.value) {
        if (
          activate_el.value.type === TYPE_IMAGE ||
          activate_el.value.type === TYPE_SHAPE
        ) {
          rotateHeight = activate_el.value.height;
          rotateWidth = activate_el.value.width;
        } else if (activate_el.value.type === TYPE_TEXT) {
          rotateHeight = parseInt(activate_el.value.fontSize || '24');
          rotateWidth = activate_el.value.width;

          // 处理文本前缀设置
          if (activate_el.value.textPrefixSet) {
            const paddLeft = activate_el.value.textPrefixPaddingLeft
              ? Number(activate_el.value.textPrefixPaddingLeft)
              : 0;
            const paddRight = activate_el.value.textPrefixPaddingRight
              ? Number(activate_el.value.textPrefixPaddingRight)
              : 0;

            if (activate_el.value.textPrefixSet === '1') {
              // 文本
              rotateWidth = activate_el.value.width + paddLeft + paddRight;
            } else if (
              activate_el.value.textPrefixSet === '2' ||
              activate_el.value.textPrefixSet === '3'
            ) {
              // 圆点+方块
              rotateWidth =
                Number(activate_el.value.textPrefixFontSize || '16') +
                paddLeft +
                paddRight;
            } else if (activate_el.value.textPrefixSet === '4') {
              // 公共图
              rotateWidth =
                (activate_el.value.textPrefixFontSize
                  ? Number(activate_el.value.textPrefixFontSize)
                  : 24) +
                paddLeft +
                paddRight;
            }
          }
        }
      }

      centerX.value = rotateLeft + rotateWidth / 2;
      centerY.value = rotateTop + rotateHeight / 2;
      originRotate.value = activate_el.value?.rotate
        ? Number(activate_el.value?.rotate)
        : 0;

      dDragFun.value = (event) => {
        if (!isRotating.value) return;
        showEleRotate(event);
      };

      eDragFun.value = (event) => {
        if (!isRotating.value) return;
        isRotating.value = false;

        // 取消动画帧
        if (rotationFrame.value) {
          cancelAnimationFrame(rotationFrame.value);
          rotationFrame.value = null;
        }

        // 更新最终数据
        if (activate_el.value) {
          activate_el.value = {
            ...activate_el.value,
            rotate: currentRotation.value,
          };
        }

        // 移除 willChange 属性
        const element = document.getElementById(activate_el.value?.element_id);
        if (element) {
          element.style.willChange = 'auto';
        }
      };
      break;
  }
};
const showEleRotate = (event: MouseEvent) => {
  if (!isRotating.value || !activate_el.value) return;

  // 取消之前的动画帧
  if (rotationFrame.value) {
    cancelAnimationFrame(rotationFrame.value);
  }

  const angle = getAngleOfThreePoint(
    [centerX.value, centerY.value],
    [mouseBeginX.value, mouseBeginY.value],
    [event.clientX, event.clientY],
  );

  const newRotate = Math.ceil(originRotate.value + angle);
  currentRotation.value = newRotate;

  // 使用 requestAnimationFrame 来更新旋转
  rotationFrame.value = requestAnimationFrame(() => {
    const element = document.getElementById(activate_el.value?.element_id);
    if (element) {
      element.style.transform = `rotate(${newRotate}deg)`;
      element.style.transformOrigin = 'center center';
      element.style.willChange = 'transform'; // 提示浏览器优化渲染
    }
  });
};

const eDrag = (event: MouseEvent) => {
  if (!s_drag_t.value) return false;

  const e_drag_t = new Date().valueOf();
  const s_drag_t_val = s_drag_t.value;
  const drag_val = drag.value;

  s_drag_t.value = null;
  drag.value = {};

  if (300 > e_drag_t - s_drag_t_val) {
    drag_val.rollBack?.();
    return false;
  }
  eDragFun.value(event);
};
// 计算三点之间的角度
const getAngleOfThreePoint = (
  centerA: number[],
  pointB: number[],
  pointC: number[],
  isRadian = false,
) => {
  // 0.根据向量法求出旋转方向
  const AB = [pointB[0] - centerA[0], pointB[1] - centerA[1]];
  const AC = [pointC[0] - centerA[0], pointC[1] - centerA[1]];
  const direct = AB[0] * AC[1] - AB[1] * AC[0]; // AB与AC叉乘求出逆时针还是顺时针旋转

  // 1.计算三条边的长度
  const lengthAB = Math.sqrt(
    Math.pow(centerA[0] - pointB[0], 2) + Math.pow(centerA[1] - pointB[1], 2),
  );
  const lengthAC = Math.sqrt(
    Math.pow(centerA[0] - pointC[0], 2) + Math.pow(centerA[1] - pointC[1], 2),
  );
  const lengthBC = Math.sqrt(
    Math.pow(pointB[0] - pointC[0], 2) + Math.pow(pointB[1] - pointC[1], 2),
  );

  // 2.使用余弦定理求出旋转角
  const cosA =
    (Math.pow(lengthAB, 2) + Math.pow(lengthAC, 2) - Math.pow(lengthBC, 2)) /
    (2 * lengthAB * lengthAC);

  // 3.转换为度数或弧度
  const angle = isRadian ? Math.acos(cosA) : (Math.acos(cosA) * 180) / Math.PI;

  return direct < 0 ? -angle : angle;
};
const dDrag = (event: MouseEvent) => {
  dDragFun.value(event);
};
// 复制画布
const copyBox = () => {
  if (!activate_box.value.id) return message.error('请选择要复制的画布');
  let act_box = activate_box.value
    ? JSON.parse(JSON.stringify(activate_box.value))
    : {}; //深克隆
  delete act_box.merge_box;
  delete act_box.merge;
  let copy_new_ele = {};
  act_box.content?.map((item, index) => {
    copy_new_ele[item] = elements.value[item];
  });
  act_box.hotLinkList?.map((item, index) => {
    copy_new_ele[item] = elements.value[item];
  });
  let copy_object = {
    controlType: 'copyBox',
    elements: {
      ...copy_new_ele,
    },
  };
  // 复制到粘贴板
  copyBoxClipboard(JSON.stringify({ ...act_box, copy_object }));
};

// 粘贴画布
const pasteBox = () => {
  if (window.navigator && window.navigator.clipboard) {
    window.navigator.permissions
      .query({ name: 'clipboard-read' })
      .then((result) => {
        if (result.state === 'granted' || result.state === 'prompt') {
          window.navigator.clipboard
            .readText()
            .then((text) => {
              let parse_box_data = {};
              try {
                parse_box_data = text ? JSON.parse(text) : {};
              } catch (error) {
                return message.info('先复制画布，再进行粘贴~~');
              }
              if (
                typeof parse_box_data === 'object' &&
                parse_box_data?.copy_object?.controlType === 'copyBox'
              ) {
                let boxId = guid2('BOX');
                addBox(boxId);
                // 处理parse_box_data，粘贴画布中的content，hotLinkList中的key,生成新的key,防止每次粘贴的key重复
                const newContent = [];
                const newHotLinkList = [];
                const newElements = {};
                const oldNewKeyMap = {}; // 用于存储旧key到新key的映射
                for (const oldKey of Object.keys(
                  parse_box_data.copy_object.elements,
                )) {
                  if (oldKey.startsWith('EL')) {
                    const newKey = guid2('EL');
                    newContent.push(newKey);
                    oldNewKeyMap[oldKey] = newKey;
                    newElements[newKey] = {
                      ...parse_box_data.copy_object.elements[oldKey],
                      element_id: newKey,
                    };
                  } else if (oldKey.startsWith('LINK')) {
                    const newKey = guid2('LINK');
                    newHotLinkList.push(newKey);
                    oldNewKeyMap[oldKey] = newKey;
                    newElements[newKey] = {
                      ...parse_box_data.copy_object.elements[oldKey],
                      element_id: newKey,
                    };
                  }
                }
                console.log('newElements', newElements);
                // 根据新旧的key值映射，更新bind_el，relative_key_x和relative_key_y和follow_show的值
                for (let elementId in newElements) {
                  let elementData = newElements[elementId];
                  if (elementData.hasOwnProperty('follow_show')) {
                    let newFollow = [];
                    if (elementData.follow_show) {
                      console.log(
                        '有跟随显示-------',
                        oldNewKeyMap,
                        elementData.follow_show,
                        oldNewKeyMap[elementData.follow_show],
                      );
                      if (
                        elementData.follow_show.includes('|') &&
                        elementData.follow_show.split('|').length > 1
                      ) {
                        // 跟随显示，多选，|分割
                        elementData.follow_show.split('|').forEach((follow) => {
                          if (oldNewKeyMap.hasOwnProperty(follow)) {
                            newFollow.push(oldNewKeyMap[follow]);
                          }
                        });
                        elementData.follow_show = newFollow.join('|');
                      } else if (
                        elementData.follow_show.includes('&') &&
                        elementData.follow_show.split('&').length > 1
                      ) {
                        // 跟随显示，多选，&分割
                        elementData.follow_show.split('&').forEach((follow) => {
                          if (oldNewKeyMap.hasOwnProperty(follow)) {
                            newFollow.push(oldNewKeyMap[follow]);
                          }
                        });
                        elementData.follow_show = newFollow.join('&');
                      } else {
                        // 跟随显示，单选
                        elementData.follow_show =
                          oldNewKeyMap[elementData.follow_show];
                      }
                    }
                  }
                  // 垂直维度
                  if (elementData.hasOwnProperty('relative_key_y')) {
                    let newRelativeKeyY = [];
                    if (elementData.relative_key_y) {
                      if (
                        elementData.relative_key_y.includes(',') &&
                        elementData.relative_key_y.split(',').length > 1
                      ) {
                        // 跟随显示，多选，|分割
                        elementData.relative_key_y
                          .split(',')
                          .forEach((relativeY) => {
                            if (oldNewKeyMap.hasOwnProperty(relativeY)) {
                              newRelativeKeyY.push(oldNewKeyMap[relativeY]);
                            }
                          });
                        elementData.relative_key_y = newRelativeKeyY.join(',');
                      } else {
                        // 垂直维度，单选
                        elementData.relative_key_y =
                          oldNewKeyMap[elementData.relative_key_y];
                      }
                    }
                  }
                }
                // 2. 递归处理对象中的所有可能包含元素key的字段
                const updateElementKeys = (obj) => {
                  if (!obj || typeof obj !== 'object') return obj;

                  // 如果是数组，递归处理每个元素
                  if (Array.isArray(obj)) {
                    return obj.map((item) => updateElementKeys(item));
                  }

                  // 如果是对象，处理每个属性
                  const newObj = {};
                  for (const [key, value] of Object.entries(obj)) {
                    if (
                      typeof value === 'string' &&
                      (value.startsWith('EL') || value.startsWith('LINK'))
                    ) {
                      // 如果值是元素key，则更新为新key
                      newObj[key] = oldNewKeyMap[value] || value;
                    } else if (typeof value === 'object' && value !== null) {
                      // 递归处理对象类型的值
                      newObj[key] = updateElementKeys(value);
                    } else {
                      // 其他类型的值保持不变
                      newObj[key] = value;
                    }
                  }
                  return newObj;
                };

                // 3. 更新所有元素中的key引用
                for (let elementId in newElements) {
                  newElements[elementId] = updateElementKeys(
                    newElements[elementId],
                  );
                }
                const newParseBoxData = {
                  ...parse_box_data,
                  id: boxId,
                  key: boxId,
                  content: newContent,
                  hotLinkList: newHotLinkList,
                  copy_object: {
                    ...parse_box_data.copy_object,
                    elements: newElements,
                  },
                };
                // 处理parse_box_data结束
                setAllBoxes({ ...boxes.value, [boxId]: newParseBoxData });
                setAllElements({
                  ...elements.value,
                  ...newParseBoxData?.copy_object?.elements,
                });
                setActiveBox({ ...boxes.value[boxId], id: boxId, key: boxId });
                scrollToActiveBox();
              } else {
                return message.info('先复制画布，再进行粘贴~~');
              }
            })
            .catch((err) => {
              console.error('Failed to read clipboard contents: ', err);
            });
        }
      });
  }
};
// 画布备注
const addRemark = () => {
  remarksVisible.value = true;
};

// Function to generate a unique GUID
const guid2 = (t = '') => {
  function S4() {
    return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
  }
  return t + (S4() + S4() + S4() + S4() + S4() + S4() + S4() + S4());
};
// 添加拖拽相关的方法
const handleDragOver = (event: DragEvent) => {
  event.preventDefault(); // 允许放置
};
// 新增元素
const handleDrop = (event: DragEvent, boxId: string) => {
  event.preventDefault();
  // 获取拖拽的数据
  const dragData = event.dataTransfer?.getData('text/plain');

  if (!dragData) return;
  let nameMap = {
    图片: '1',
    文本: '2',
    图形: 'SHAPE',
    热区: 'HOT_LINK',
  };
  try {
    const elType = nameMap[dragData];
    const subName = '新增元素';
    // 获取放置位置
    const boxElement = (event.currentTarget as HTMLElement).closest(
      '.box_item',
    ) as HTMLElement;
    const cvDivPosition = boxElement.getBoundingClientRect();
    const x = event.clientX - cvDivPosition.left;
    const y = event.clientY - cvDivPosition.top;
    // 根据元素类型创建新元素
    let newElement: ElementType = {
      element_id: '',
      follow_show: '',
      relative_key_x: '',
      relative_key_y: '',
      relative_x: '5',
      relative_y: '2',
      type: elType,
      x: x,
      y: y,
      zIndex: '1',
      box_id: boxId,
    };
    let DRAG_HOT_width = 0;
    let DRAG_HOT_height = 0;
    // 设置热区的范围，根据元素大小设置热区的宽高
    if (activate_el.value && activate_el.value.type !== DRAG_HOT) {
      let active_el_data = JSON.parse(JSON.stringify(activate_el.value));
      let elX = parseInt(event.clientX - cvDivPosition.left) - 20;
      let elY = parseInt(event.clientY - cvDivPosition.top) - 45;

      let active_el_width = Number(active_el_data?.width);
      let active_el_height =
        active_el_data?.type == '1'
          ? Number(active_el_data?.height)
          : Number(active_el_data?.fontSize);
      let leftMin = parseInt(Number(active_el_data?.x) - 80),
        leftMax = parseInt(
          Number(active_el_data?.x) + (active_el_width - active_el_width / 3),
        ),
        topMin = parseInt(Number(active_el_data?.y) - 80),
        topMax = parseInt(
          Number(active_el_data?.y) + (active_el_height - active_el_height / 3),
        );
      // 如果元素1/3宽高大于90（激活热区宽高），则限制热区不超出元素
      if (active_el_width / 3 > 90) {
        leftMax = parseInt(Number(active_el_data.x) + (active_el_width - 90));
      }
      if (active_el_height / 3 > 90) {
        topMax = parseInt(Number(active_el_data.y) + (active_el_height - 90));
      }
      if (elType === DRAG_HOT) {
        //判断热区是否在元素范围内
        if (elX > leftMin && elX < leftMax && elY > topMin && elY < topMax) {
          DRAG_HOT_width = active_el_width;
          DRAG_HOT_height = active_el_height;
          let targetEl = document.getElementById(
            `${active_el_data.element_id}`,
          );

          let left = targetEl?.style.left
            ? targetEl?.style.left.split('px')[0]
            : 0;
          let top = targetEl?.style.top
            ? targetEl?.style.top.split('px')[0]
            : 0;
          newElement = {
            ...newElement,
            x: left,
            y: top,
            zIndex: '1',
          };
        }
      }
    }
    // 根据不同类型设置特定属性
    switch (elType) {
      case TYPE_IMAGE:
        newElement = {
          ...newElement,
          name: subName,
          height: '150',
          width: '200',
          imgHeight: '150',
          imgWidth: '200',
          type: elType,
          imgType: '1',
          padding: '20',
          imageProcess: [],
          bg_color_set: '0', //设置类型默认为无背景色
        };
        for (let index = 0; index <= 11; index++) {
          const initProcess = { type: '', value1: '', value2: '' };
          newElement?.imageProcess.push(initProcess);
        }
        break;
      case TYPE_TEXT:
        newElement = {
          ...newElement,
          name: subName,
          fontSize: '24',
          lineHeight: '24',
          color: '#000000FF',
          width: '200',
          type: elType,
          textType: '2',
          fontCFamily: '',
          fontEFamily: '',
          text_rotate: '1', //循环处理默认为无
          if_set: '0', //拼接数据默认为否
          setTextAct: '0', //行为扩展设置，开关默认为关闭
          text_set_punctuation: 'SEPARATOR_1', //拼接符号默认为顿号
          single_line_height: '0', //换行影响范围默认为：所有手动换行
        };
        break;
      case TYPE_SHAPE:
        newElement = {
          ...newElement,
          name: subName,
          height: '20',
          width: '200',
          type: elType,
          rotate: 0,
          shapeType: '1', // 箭头类型默认为实线右箭头
          bor_color: '#000', //边框颜色
          fill_color: '#fff', // 填充颜色
          bg_color_set: '0', //设置类型默认为无背景色
          imageProcess: [],
        };
        for (let index = 0; index <= 11; index++) {
          const initProcess = { type: '', value1: '', value2: '' };
          newElement?.imageProcess.push(initProcess);
        }
        break;
      case TYPE_HOT:
        newElement = {
          ...newElement,
          name: subName,
          item_id: '',
          target: '',
          height: DRAG_HOT_height || 200,
          width: DRAG_HOT_width || 200,
          type: elType,
          actBind1: null,
          actBind2: null,
        };
        break;
    }
    // 初始数据
    addElement(newElement, boxId, 'drop');
  } catch (error) {
    console.error('Error handling drop:', error);
  }
};
const textPrefixStyle = (data: any) => {
  let styleObj = {};
  // textPrefix  textPrefixFont  textPrefixFontColor textPrefixFontSize textPrefixFontSite textPrefixType
  if (data.textPrefixSet == 4) {
    styleObj.height = data.textPrefixFontSize || data.lineHeight + 'px';
  } else {
    styleObj.height = 'auto';
  }
  styleObj.fontFamily = data.textPrefixFont;
  styleObj.color = data.textPrefixFontColor || data.color;
  styleObj.fontSize = (data.textPrefixFontSize || data.fontSize) + 'px';
  styleObj.paddingLeft = (data.textPrefixPaddingLeft || 0) + 'px';
  styleObj.paddingRight = (data.textPrefixPaddingRight || 0) + 'px';
  return styleObj;
};
const filterText = (element: any) => {
  let text = element.defaultText || element.name;
  if (!text) return '';
  let ss = '';
  let prefix = '';
  // const lines = text.split('\r\n');
  const lines = text?.split('\n');
  let style = textPrefixStyle(element);
  if (element.textPrefixSet != 0) {
    if (element.textPrefixSet == 1) {
      //文本
      let styleStr =
        `height:${style.height};font-family:${style.fontFamily};color:${style.color};font-size:${style.fontSize};padding-left:${style.paddingLeft};padding-right:${style.paddingRight};display:inline-block;vertical-align: middle;` +
        `word-wrap: break-word;overflow-wrap: break-word;word-break: keep-all;`;
      prefix = `<span style='${styleStr}'>${element.textPrefix || ''}</span>`;
    } else if (element.textPrefixSet == 2 || element.textPrefixSet == 3) {
      //圆点/方块
      let lineGap = 0;
      if (element.lineHeight > element.fontSize) {
        lineGap = Math.floor((element.lineHeight - element.fontSize) / 2);
      }
      let paddingTop =
        Math.floor(
          Number(element.fontSize) / 2 - Number(element.textPrefixFontSize) / 2,
        ) + lineGap;
      let styleStr = `height:${style.height};padding-left:${style.paddingLeft};padding-right:${style.paddingRight};display:flex;vertical-align:middle;padding-top:${paddingTop}px;`;
      let styleshape = `display:inline-block;width:${element.textPrefixFontSize || '6'}px;height:${element.textPrefixFontSize || '6'}px;background:${style.color || '#000'};margin-bottom: 25%;`;
      let textType =
        element.textPrefixSet == 2
          ? `<div style="${styleshape};border-radius:50%;"></div>`
          : `<div style="${styleshape};"></div>`;
      prefix = `<span style=${styleStr}>${textType}</span>`;
    } else if (element.textPrefixSet == 4 && element.colorBlockImageUrl) {
      // 公共图
      const marginBottom =
        (Number(element.lineHeight) - Number(style.height)) / 2;
      let styleStr = `height:${style.height};width:${style.height};padding-left:${style.paddingLeft};padding-right:${style.paddingRight};margin-bottom: ${marginBottom}px;display:inline-block;`;
      prefix = `<img style='${styleStr}' loading="lazy" src="${element.colorBlockImageUrl}" alt="">`;
    }
  }
  // console.log("prefix", prefix);

  for (let i = 0; i < lines?.length; i++) {
    let lineSpan = '';
    let currentWord = ''; // 用于收集连续英文字符
    for (let j = 0; j < lines[i].length; j++) {
      const char = lines[i][j];
      const charCode = char.charCodeAt(0);
      // 判断是否为英文字母或数字（根据需求扩展）
      if (charCode <= 255 && /[a-zA-Z0-9]/.test(char)) {
        currentWord += char;
      } else {
        if (currentWord) {
          lineSpan += `<span style="font-family:${element.fontEFamily};font-size:${element.fontSizeEnglish || element.fontSize}px;">${currentWord}</span>`;
          currentWord = '';
        }
        // 处理非英文字符
        const fontFamily =
          charCode > 255 ? element.fontCFamily : element.fontEFamily;
        lineSpan += `<span style="font-family:${fontFamily};letter-spacing:${j == lines[i].length - 1 ? 0 : element.letterSpacing}px;">${char === ' ' ? ' ' : char}</span>`;
      }
    }
    // 处理行末剩余的英文字符
    if (currentWord) {
      lineSpan += `<span style="font-family:${element.fontEFamily};font-size:${element.fontSizeEnglish || element.fontSize}px;">${currentWord}</span>`;
    }
    if (lineSpan == '') {
      ss += '<br>';
    } else {
      if (element.single_line_height === '0') {
        //所有手动换行
        if (
          element.textPrefixFontSite === '0' ||
          element.textPrefixSet == 2 ||
          element.textPrefixSet == 3 ||
          element.textPrefixSet == 4
        ) {
          //文案左侧,圆点/方块/公共图默认在左侧
          ss += `<div  style="display:flex;width:100%;">${prefix}<div style="width:100%;white-space: pre-wrap;">${lineSpan}</div></div>`;
        } else {
          //文案右侧
          ss += `<div  style="display:flex;width:100%;"><div style="width:100%;white-space: pre-wrap;">${lineSpan}</div>${prefix}</div>`;
        }
      } else if (element.single_line_height === '1' && i === 0) {
        //手动换行第一行
        if (
          element.textPrefixFontSite === '0' ||
          element.textPrefixSet == 2 ||
          element.textPrefixSet == 3 ||
          element.textPrefixSet == 4
        ) {
          //文案左侧，圆点/方块/公共图默认在左侧
          ss += `<div style="display:flex;width:100%;">${prefix}<div style="width:100%;white-space: pre-wrap;">${lineSpan}</div></div>`;
        } else {
          //文案右侧
          ss += `<div style="display:flex;width:100%;"><div style="width:100%;white-space: pre-wrap;">${lineSpan}</div>${prefix}</div>`;
        }
      } else {
        ss += `<span>${lineSpan}</span><br>`;
      }
    }
  }
  return ss;
};
// 解析SVG Data URL 并返回 SVG 字符串
function parseSvgDataUrl(dataUrl) {
  try {
    // 1. 检查是否是 Data URL
    if (!dataUrl.startsWith('data:image/svg+xml,')) {
      throw new Error('Invalid SVG Data URL');
    }

    // 2. 提取 SVG 部分（去掉 `data:image/svg+xml,`）
    const svgPart = dataUrl.substring('data:image/svg+xml,'.length);

    // 3. 尝试直接解码（适用于标准 URI 编码）
    try {
      return decodeURIComponent(svgPart);
    } catch (e) {
      // 4. 如果失败，可能是特殊字符（如 `'` 未编码），尝试直接返回
      return svgPart;
    }
  } catch (e) {
    console.error('Failed to parse SVG Data URL:', e);
    return ''; // 返回空字符串或默认 SVG
  }
}

// 在组件挂载时处理所有图片和图形
onMounted(async () => {
  // 处理未处理过的图片
  const unprocessedImages = Object.entries(elements.value).filter(
    ([key, el]) =>
      el?.type === TYPE_IMAGE && !processedImages.has(el.element_id),
  );

  if (unprocessedImages.length > 0) {
    await imageProcessor.processImages(Object.fromEntries(unprocessedImages));
    unprocessedImages.forEach(([_, el]) => processedImages.add(el.element_id));
  }

  // 处理未处理过的图形
  const unprocessedShapes = Object.entries(elements.value).filter(
    ([key, el]) =>
      el?.type === TYPE_SHAPE && !processedShapes.has(el.element_id),
  );

  if (unprocessedShapes.length > 0) {
    await shapeProcessor.processShapes(Object.fromEntries(unprocessedShapes));
    unprocessedShapes.forEach(([_, el]) => processedShapes.add(el.element_id));
  }
});
const imgIconFontSize = (width: number, height: number) => {
  if (width < height) {
    if (width < 10) {
      return width / 2 + 2 + 'px';
    } else if (width < 30) {
      return width / 2 + 5 + 'px';
    } else {
      return width / 2 + 'px';
    }
  } else {
    if (height < 30) {
      return height / 2 + 5 + 'px';
    } else {
      return height / 2 + 'px';
    }
  }
};
const setDefaultImage = ($event: any) => {
  const img = $event.target as HTMLImageElement;
  // 防止递归
  if (img.src.endsWith(`${defaultImg}`)) return;

  img.src = `${defaultImg}`;
  img.onerror = null; // 移除错误处理器防止循环
};
// 监听元素变化，处理新增的图片和图形
watch(
  () => elements.value,
  async (newElements) => {
    // 处理新增的图片
    const unprocessedImages = Object.entries(newElements).filter(
      ([key, el]) =>
        el?.type === TYPE_IMAGE && !processedImages.has(el.element_id),
    );

    if (unprocessedImages.length > 0) {
      await imageProcessor.processImages(Object.fromEntries(unprocessedImages));
      unprocessedImages.forEach(([_, el]) =>
        processedImages.add(el.element_id),
      );
    }

    // 处理新增的图形
    const unprocessedShapes = Object.entries(newElements).filter(
      ([key, el]) =>
        el?.type === TYPE_SHAPE && !processedShapes.has(el.element_id),
    );

    if (unprocessedShapes.length > 0) {
      await shapeProcessor.processShapes(Object.fromEntries(unprocessedShapes));
      unprocessedShapes.forEach(([_, el]) =>
        processedShapes.add(el.element_id),
      );
    }
  },
  { deep: true },
);
</script>

<template>
  <div
    class="canvas-container absolute top-4 flex h-[94%] w-[calc(100%)] justify-center overflow-auto"
    @mouseup="eDrag"
    @mousemove="dDrag"
  >
    <div
      class="fixed left-80 top-1/3 z-[1001] ml-10 w-10 rounded border border-gray-200 bg-white"
    >
      <div
        class="cursor-pointer p-2.5 text-gray-600 hover:text-blue-500"
        @click.stop="moveBox('up')"
        id="cv_tool_up"
      >
        <i class="font_family icon-chevron-up"></i>
      </div>
      <div
        class="cursor-pointer p-2.5 text-gray-600 hover:text-blue-500"
        @click.stop="moveBox('down')"
      >
        <i class="font_family icon-chevron-down"></i>
      </div>
      <!-- 备注 -->
      <div
        class="cursor-pointer p-2.5 text-gray-600 hover:text-blue-500"
        @click="addRemark"
        title="备注"
      >
        <i class="font_family icon-chat"></i>
      </div>
      <!-- 复制 -->
      <div
        class="cv_tool_add cursor-pointer p-2.5 text-gray-600 hover:text-blue-500"
        @click="copyBox"
        title="复制"
      >
        <i class="font_family icon-library_add"></i>
      </div>
      <!-- 粘贴 -->
      <div
        class="cursor-pointer p-2.5 text-gray-600 hover:text-blue-500"
        id="cv_tool_parse"
        @click="pasteBox"
        title="粘贴"
      >
        <i class="font_family icon-library_add_check"></i>
      </div>
      <div
        class="cursor-pointer p-2.5 text-gray-600 hover:text-blue-500"
        @click="delBox"
        title="删除"
      >
        <i class="font_family icon-delete"></i>
      </div>
      <!-- 添加画布 -->
      <div
        class="cursor-pointer p-2.5 text-gray-600 hover:text-blue-500"
        @click="addBox()"
      >
        <i class="font_family icon-add"></i>
      </div>
    </div>
    <div>
      <div
        v-for="box in boxesKeys"
        v-show="boxes[box]?.box_show"
        :key="box"
        :class="[
          'box_item content-box relative mb-4 border bg-white',
          { 'border-blue-500': box == activate_box.id },
          { active_box: box == activate_box.id },
        ]"
        :box-key="box"
        :style="boxStyleObject(boxes[box])"
        :data-id="box"
        :module-id="
          modules.find((item) => item.boxes.includes(box))?.module_key
        "
        @click.stop="
          templateStore.setActiveBox(
            { ...boxes[box], key: box, id: boxes[box]?.id },
            modules.find((item) => item.boxes.includes(box)),
          )
        "
      >
        <div
          class="relative h-full w-full overflow-y-clip"
          @dragover="handleDragOver"
          @drop="(event) => handleDrop(event, box)"
        >
          <!-- 统一处理所有元素 -->
          <template v-for="el_key in boxes[box]?.content">
            <div
              :class="[
                'absolute',
                activate_el?.element_id == el_key
                  ? 'active_el box-border box-content cursor-pointer border border-dashed border-blue-500'
                  : 'cursor-move',
              ]"
              v-if="elements[el_key]"
              @click="activate_el = elements[el_key]"
              :style="getElementStyle(elements[el_key])"
              :data-pid="el_key"
              :data-id="el_key"
              :box-id="box"
              :id="el_key"
              :key="`${el_key}_${box}`"
              :data-type="elements[el_key]?.type"
            >
              <!-- 图片元素 -->
              <template v-if="elements[el_key]?.type == TYPE_IMAGE">
                <img
                  v-if="
                    (elements[el_key]?.imgType == 3 &&
                      elements[el_key]?.memoryImgPath) ||
                    elements[el_key]?.local_url
                  "
                  loading="lazy"
                  class="box-content h-full w-full object-contain"
                  @mousedown="sDrag('EL_MOVE', $event, elements[el_key], box)"
                  :src="
                    imageProcessor.getImageUrl(elements[el_key]?.element_id) ||
                    elements[el_key]?.memoryImgPath ||
                    elements[el_key]?.local_url ||
                    '/image/icon/thumbnail-new.png'
                  "
                  :id="`img${el_key}`"
                  :style="{
                    display:
                      elements[el_key]?.imgType == 4 &&
                      elements[el_key]?.bg_color
                        ? 'none'
                        : 'inline-block',
                  }"
                  crossorigin="anonymous"
                  @load="
                    () => {
                      if (!processedImages.has(elements[el_key]?.element_id)) {
                        imageProcessor.processImage(elements[el_key]);
                        processedImages.add(elements[el_key]?.element_id);
                      }
                    }
                  "
                  @error="setDefaultImage($event, el_key, box)"
                  :onerror="''"
                />
                <img
                  v-else
                  src="/image/icon/thumbnail-new.png"
                  :class="
                    activate_el?.element_id == el_key
                      ? 'bg-[#c0c4e87a]'
                      : 'bg-[#99999910]'
                  "
                  loading="lazy"
                  @mousedown="sDrag('EL_MOVE', $event, elements[el_key], box)"
                  :id="`img${el_key}`"
                  :style="{
                    display:
                      elements[el_key]?.imgType == 4 &&
                      elements[el_key]?.bg_color
                        ? 'none'
                        : 'inline-block',
                    width: '100%',
                    height: '100%',
                  }"
                />

                <canvas
                  class="hidden"
                  :width="activate_el?.width"
                  :height="activate_el?.height"
                  :id="`canvas_${el_key}`"
                  :key="`canvas_${el_key}`"
                ></canvas>
                <div
                  v-show="
                    elements[el_key]?.imgType == 4 && elements[el_key]?.bg_color
                  "
                  class="h-full w-full"
                  @mousedown="sDrag('EL_MOVE', $event, elements[el_key], box)"
                ></div>
              </template>

              <!-- 文字元素 -->
              <template v-if="elements[el_key]?.type == TYPE_TEXT">
                <div
                  class="h-full w-full overflow-hidden text-ellipsis"
                  @mousedown="sDrag('EL_MOVE', $event, elements[el_key], box)"
                >
                  <div v-html="filterText(elements[el_key])"></div>
                </div>
              </template>

              <!-- 图形元素 -->
              <template v-if="elements[el_key]?.type == 'SHAPE'">
                <div
                  class="h-full w-full"
                  @mousedown="sDrag('EL_MOVE', $event, elements[el_key], box)"
                >
                  <div
                    class="pointer-events-none h-full w-full"
                    :data="shapeImg(elements[el_key])"
                    type="image/svg+xml"
                    @error="
                      $event.target.data = '/image/shape_arrow/arrow_right.svg'
                    "
                    :id="`arrow${el_key}`"
                    v-html="parseSvgDataUrl(shapeImg(elements[el_key]))"
                    @load="
                      () => {
                        if (
                          !processedShapes.has(elements[el_key]?.element_id)
                        ) {
                          shapeProcessor.processShape(elements[el_key]);
                          processedShapes.add(elements[el_key]?.element_id);
                        }
                      }
                    "
                  ></div>
                </div>
                <canvas
                  class="hidden"
                  :width="activate_el?.data?.width"
                  :height="activate_el?.data?.height"
                  :id="`canvas_${el_key}`"
                  :key="`canvas_${el_key}`"
                ></canvas>
              </template>

              <!-- 通用操作按钮 -->
              <!-- 改变大小 -->
              <div
                class="absolute -bottom-1 -right-1 h-3 w-3 cursor-nwse-resize bg-blue-500"
                @mousedown="
                  sDrag('EL_CHANGE_SIZE', $event, elements[el_key], box)
                "
                v-show="
                  activate_el?.element_id == el_key &&
                  String(elements[el_key]?.type) !== TYPE_TEXT
                "
              ></div>
              <!-- 旋转 -->
              <div
                class="absolute -bottom-7 left-1/2 flex h-[24px] w-[24px] -translate-x-1/2 cursor-pointer items-center justify-center rounded-full bg-[#1890ff] text-white"
                @mousedown="
                  sDrag('EL_CHANGE_ROTATE', $event, elements[el_key], box)
                "
                v-show="activate_el?.element_id == el_key"
              >
                <i
                  class="font_family icon-xuanzhuan font-size-16px w-[16px]"
                ></i>
              </div>
              <!-- 删除元素 -->
              <div
                class="absolute -right-2.5 -top-2.5 cursor-pointer"
                @click="delElement(box, el_key)"
                data-method="offset"
                data-type="auto"
                data-opera="delete"
                v-show="activate_el?.element_id == el_key"
              >
                <img class="h-4 w-4" src="/image/icon/del-cir.png" alt="" />
              </div>
            </div>
          </template>
          <!-- 统一处理热区元素 -->
          <template v-if="boxes[box]?.hotLinkList?.length">
            <div
              v-for="el_key in boxes[box]?.hotLinkList"
              :key="`hotlink_${el_key}`"
              :class="[
                'absolute',
                activate_el?.element_id == el_key
                  ? 'active_el box-border cursor-pointer border border-dashed border-blue-500'
                  : 'cursor-move',
              ]"
              @click="activate_el = elements[el_key]"
              :style="getElementStyle(elements[el_key])"
              :data-pid="el_key"
              :data-id="el_key"
              :id="el_key"
            >
              <!-- 热区元素 -->
              <template v-if="elements[el_key]?.type == TYPE_HOT">
                <div
                  class="h-full w-full bg-[#0099CC66]"
                  @mousedown="sDrag('EL_MOVE', $event, elements[el_key], box)"
                ></div>
              </template>

              <!-- 通用操作按钮 -->
              <div
                class="absolute -bottom-1 -right-1 h-3 w-3 cursor-nwse-resize bg-blue-500"
                @mousedown="
                  sDrag('EL_CHANGE_SIZE', $event, elements[el_key], box)
                "
                v-show="activate_el?.element_id == el_key"
              ></div>
              <div
                class="absolute -right-2.5 -top-2.5 cursor-pointer"
                @click="delElement(box, el_key)"
                data-method="offset"
                data-type="auto"
                data-opera="delete"
                v-show="activate_el?.element_id == el_key"
              >
                <img class="h-4 w-4" src="/image/icon/del-cir.png" alt="" />
              </div>
            </div>
          </template>
        </div>
        <div
          v-if="box === activate_box.id"
          class="absolute bottom-[-4px] left-[calc(50%-25px)] z-[999] h-[8px] w-[50px] cursor-ns-resize rounded-[50px] bg-[#1890ff]"
          @mousedown="
            sDrag('BOX_HEIGHT', $event, elements[activate_el?.element_id], box)
          "
        ></div>
      </div>
    </div>
    <BoxRemarks
      :visible="remarksVisible"
      :close="() => (remarksVisible = false)"
    />
  </div>
</template>

<style scoped>
.vue-ruler-wrapper {
  position: absolute;
  width: 100% !important;
}
.vue-ruler-h,
.vue-ruler-v {
  opacity: 1;
}
</style>
