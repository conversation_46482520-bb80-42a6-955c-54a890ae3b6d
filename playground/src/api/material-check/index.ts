import { requestClient, formPostRequestClient } from '#/api/request';


/**
 * 获取实物质检数据
 */
async function getPhysicalCheckInfo(data: any) {
  console.log('getPhysicalCheckInfo---', data);
  return requestClient.get(
    //`admin/pim/goodsTemplateTool/ajaxGetTool?id=910`
    // http://omni_wxl.local/admin/pim/health_physical_check/info?goods_sn=00SYUN0NAZE&action_shop_page_id=2985
    `admin/pim/health_physical_check/info?goods_sn=${data.goods_sn}&action_shop_page_id=${data.action_shop_page_id}`
  );
}
/**
 * 校验通过/二次质检/确认异常
 */
async function postPhysicalCheck(data: any) {
  return formPostRequestClient.post(
    `/admin/pim/health_physical_check/check`,
    data,
  );
}
/**
 * 校验通过
 */
async function postPhysicalPass(data: any) {
  return formPostRequestClient.post(
    `/admin/pim/health_physical_check/pass`,
    data,
  );
}
/**
 * 删除异常
 */
async function deleteErrors(data: any) {
  return formPostRequestClient.post(
    `/admin/pim/health_physical_check/del`,
    data,
  );
}


/**
 * 上传图片
 */
function uploadImage(data: any) {
  return `${import.meta.env.VITE_API_URL}/admin/pub/uploadImage`;
}


export { getPhysicalCheckInfo, postPhysicalCheck, uploadImage, deleteErrors, postPhysicalPass };
