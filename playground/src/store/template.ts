import { defineStore } from 'pinia';
import { ref, shallowRef, watch, nextTick, createVNode, onUnmounted, onMounted } from 'vue';
import {
  getTemplateInfo,
  saveTemplateApi,
  commitTemplateBackup,
  commitbackVersion,
  getPreviewData,
} from '#/api/template';
import { useRouter } from 'vue-router';
import { message, Modal } from 'ant-design-vue';
import { useAuthStore } from '#/store/auth';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import arrow_right from '../../image/shape_arrow/arrow_right.svg';
import arrow_right_dashed from '../../image/shape_arrow/arrow_right_dashed.svg';
import arrow_double_dashed from '../../image/shape_arrow/arrow_double_dashed.svg';
import line_solid from '../../image/shape_arrow/line_solid.svg';
import line_dashed from '../../image/shape_arrow/line_dashed.svg';
import arrow_double from '../../image/shape_arrow/arrow_double.svg';
import type {
  BoxType,
  ElementType,
  ModuleType,
  ToolType,
  PreviewDataType,
  OptionType,
  TemplateInfo,
} from './types/template';
import { isEqual } from '@vben/utils';

export const useTemplateStore = defineStore('template', () => {
  const authStore = useAuthStore();
  const templateInfo = ref<TemplateInfo | null>(null);
  const loading = ref(false);
  const error = ref<string | null>(null);
  const activate_box = ref<BoxType | null>(null);
  const activate_el = ref<ElementType | null>(null);
  const activate_module = ref<ModuleType | null>(null);
  const modules = shallowRef<ModuleType[]>([]);
  const modulesObj = ref<ModuleType | null>(null);
  const tool = ref<ToolType | null>(null);
  const template_info_other_array = ref<any>(null);
  const boxes = shallowRef<BoxType[]>([]);
  const boxesKeys = ref<string[]>([]);
  const elements = shallowRef<Record<string, ElementType>>({});
  const shop_list = ref<OptionType[]>([]);
  const text_zdy_sep_opt = ref<OptionType[]>([]);
  const template_field = ref<OptionType[]>([]);
  const template_title = ref<any>(null);
  const platform_list = ref<any>(null);
  const version_record = ref<any>(null);
  const back_version_url = ref<string | null>(null);
  const version = ref<string | null>('0.2');
  const previewData = ref<PreviewDataType>({
    previewShop: '',
    product_id: '',
  });
  const sales_brand = ref<string | null>(null);
  const previewId = ref<number | null>(null);
  const isPreview = ref<boolean>(false); // 是否预览状态
  const isRevokeAndRecovery = ref<boolean>(false); // 是否正在撤销恢复
  // 模块菜单控制
  const expandedModules = ref<Record<string, boolean>>({});
  const expandedBoxes = ref<Record<string, boolean>>({});
  const expandedElements = ref<Record<string, boolean>>({});
  const typeMap = ref({
    '1': 'img',
    '2': 'text',
    HOT_LINK: 'hotspot',
    SHAPE: 'arrow',
  });
  const template_public_image = ref<OptionType[]>([]);
  const text_system_field = ref<any>([]);
  const template_excel_hot_value = ref<any>([]);
  const font = ref<OptionType[]>([]); // 字体
  const diff_font = ref<any>([]); // 未识别字体
  // 图形类型映射地址
  const shapeImgConfig = ref({
    1: arrow_right,
    2: arrow_right_dashed,
    3: arrow_double_dashed,
    4: line_solid,
    5: line_dashed,
    6: arrow_double,
  });
  const router = useRouter();
  const id = ref(router.currentRoute.value.query.id || '');
  const action_shop_page_id = ref(
    router.currentRoute.value.query.action_shop_page_id || '',
  );
  console.log(
    'outer.currentRoute.value.query',
    router.currentRoute.value.query,
  );

  // 版本控制相关状态
  const versionHistory = shallowRef<any[]>([]); // 存储版本历史
  const currentVersionIndex = ref<number>(-1); // 当前版本索引
  const maxVersionCount = 20; // 最大版本数量
  const canRepeal = ref<boolean>(false); // 是否可以撤销
  const canRecover = ref<boolean>(false); // 是否可以恢复

  // 检查两个版本是否相同
  const isVersionEqual = (version1: any, version2: any) => {
    if (!version1 || !version2) return false;
    return JSON.stringify(version1) === JSON.stringify(version2);
  };

  // 自定义节流函数实现
  const throttle = <T extends (...args: any[]) => any>(
    func: T,
    wait: number,
  ): ((...args: Parameters<T>) => ReturnType<T> | undefined) & {
    cancel: () => void;
  } => {
    let timeout: ReturnType<typeof setTimeout> | null = null;
    let lastRun = 0;

    // 添加取消方法
    function cancel() {
      if (timeout) {
        clearTimeout(timeout);
        timeout = null;
      }
    }

    function throttled(...args: Parameters<T>): ReturnType<T> | undefined {
      const now = Date.now();
      const remaining = wait - (now - lastRun);

      if (remaining <= 0 || remaining > wait) {
        // 可以立即执行
        if (timeout) {
          clearTimeout(timeout);
          timeout = null;
        }
        lastRun = now;
        return func(...args);
      } else if (!timeout) {
        // 设置延迟执行
        timeout = setTimeout(() => {
          lastRun = Date.now();
          timeout = null;
          func(...args);
        }, remaining);
      }
      return undefined;
    }

    // 添加取消方法到返回的函数
    throttled.cancel = cancel;

    return throttled;
  };

  // 添加 IndexedDB 存储方法
  const saveToIndexedDB = async (key: string, data: any) => {

		 // 检查数据大小，如果超过阈值则禁用撤销/恢复功能
    const dataSize = new Blob([JSON.stringify(data)]).size / (1024 * 1024); // 转换为MB
    const MAX_DATA_SIZE = 3; // 5MB阈值，可根据需要调整
    
    if (dataSize > MAX_DATA_SIZE) {
      console.warn(`Data size (${dataSize.toFixed(2)}MB) exceeds limit. Disabling undo/redo.`);
      // 清空历史记录并禁用撤销/恢复
      versionHistory.value = [];
      currentVersionIndex.value = -1;
      updateButtonState();
      // message.warning('数据量过大，已禁用撤销/恢复功能以提高性能');
      return;
    }
    
    return new Promise((resolve, reject) => {
      const request = indexedDB.open('templateDB', 1);
      
      request.onupgradeneeded = () => {
        const db = request.result;
        if (!db.objectStoreNames.contains('templates')) {
          db.createObjectStore('templates', { keyPath: 'id' });
        }
      };
      
      request.onsuccess = () => {
        const db = request.result;
        const tx = db.transaction('templates', 'readwrite');
        const store = tx.objectStore('templates');
        
        store.put({ id: key, data });
        
        tx.oncomplete = () => resolve(true);
        tx.onerror = (e) => reject(e);
      };
      
      request.onerror = (e) => reject(e);
    });
  };

  // 保存当前版本
  const saveVersion = throttle(async () => {
    console.log('Attempting to save version, isPreview:', isPreview.value);
    // 如果正在预览,不保存版本
    if (isPreview.value) return;

    const currentTool = {
      box: JSON.parse(JSON.stringify(boxes.value)),
      modules: JSON.parse(JSON.stringify(modules.value)),
      element: JSON.parse(JSON.stringify(elements.value)),
      version: version.value,
    };
    
    // 检查数据大小，如果超过阈值则禁用撤销/恢复功能
    const dataSize = new Blob([JSON.stringify(currentTool)]).size / (1024 * 1024); // 转换为MB
    const MAX_DATA_SIZE = 3; // 5MB阈值，可根据需要调整
    
    if (dataSize > MAX_DATA_SIZE) {
      console.warn(`Data size (${dataSize.toFixed(2)}MB) exceeds limit. Disabling undo/redo.`);
      // 清空历史记录并禁用撤销/恢复
      versionHistory.value = [];
      currentVersionIndex.value = -1;
      updateButtonState();
      message.warning('数据量过大，已禁用撤销/恢复功能以提高性能');
      return;
    }
    
    // 检查是否与最新版本相同
    if (versionHistory.value.length > 0) {
      const latestVersion =
        versionHistory.value[versionHistory.value.length - 1];
      if (isVersionEqual(currentTool, latestVersion)) {
        return; // 如果与最新版本相同，不保存
      }
    }

    // 如果当前不是最新版本,删除当前版本之后的所有版本
    if (currentVersionIndex.value < versionHistory.value.length - 1) {
      versionHistory.value = versionHistory.value.slice(
        0,
        currentVersionIndex.value + 1,
      );
    }
    // 添加新版本
    versionHistory.value.push(currentTool);
    currentVersionIndex.value = versionHistory.value.length - 1;

    // 如果超过最大版本数,删除最早的版本
    if (versionHistory.value.length > maxVersionCount) {
      versionHistory.value.shift();
      currentVersionIndex.value--;
    }

    // 更新按钮状态
    updateButtonState();

    // 使用 IndexedDB 替代 localStorage
    try {
      await saveToIndexedDB('template_version_history', {
        history: versionHistory.value,
        currentIndex: currentVersionIndex.value,
      });
    } catch (e) {
      console.warn('Failed to save version history:', e);
      // 如果保存失败,回退到上一个状态
      versionHistory.value.pop();
      currentVersionIndex.value--;
      updateButtonState();
    }
  }, 1500); // 1.5秒的节流时间

  // 更新按钮状态
  const updateButtonState = () => {
    canRepeal.value = currentVersionIndex.value > 0;
    canRecover.value =
      currentVersionIndex.value < versionHistory.value.length - 1;
  };

  // 撤销
  const handleRepeal = () => {
    if (!canRepeal.value) {
      message.info('没有可撤销的版本');
      return;
    }
    isRevokeAndRecovery.value = true;
    currentVersionIndex.value--;
    const prevVersion = JSON.parse(
      JSON.stringify(versionHistory.value[currentVersionIndex.value]),
    );

    // 更新数据
    boxes.value = JSON.parse(JSON.stringify(prevVersion.box));
    modules.value = JSON.parse(JSON.stringify(prevVersion.modules));
    elements.value = JSON.parse(JSON.stringify(prevVersion.element));
    version.value = JSON.parse(JSON.stringify(prevVersion.version));

    // 更新现在激活的元素
    if (activate_el.value?.element_id) {
      activate_el.value = elements.value[activate_el.value.element_id];
			console.log('handleRepeal--更新现在激活的元素--activate_el.value', activate_el.value);
    }

    // 更新激活状态
    updateActiveState(prevVersion);

    // 更新按钮状态
    updateButtonState();
    // 更新 localStorage，只更新 currentIndex
    try {
      localStorage.setItem(
        'template_version_history',
        JSON.stringify({
          history: versionHistory.value,
          currentIndex: currentVersionIndex.value,
        }),
      );
    } catch (e) {
      console.warn('Failed to update version history:', e);
    }
    setTimeout(() => {
      isRevokeAndRecovery.value = false;
    }, 500);
  };

  // 恢复
  const handleRecover = () => {
    if (!canRecover.value) {
      message.info('没有可恢复的版本');
      return;
    }
    isRevokeAndRecovery.value = true;
    currentVersionIndex.value++;
    const nextVersion = versionHistory.value[currentVersionIndex.value];

    // 更新数据
    boxes.value = JSON.parse(JSON.stringify(nextVersion.box));
    modules.value = JSON.parse(JSON.stringify(nextVersion.modules));
    elements.value = JSON.parse(JSON.stringify(nextVersion.element));
    version.value = JSON.parse(JSON.stringify(nextVersion.version));
    // 更新现在激活的元素
    if (activate_el.value?.element_id) {
      activate_el.value = elements.value[activate_el.value.element_id];
			console.log('handleRecover--更新现在激活的元素--activate_el.value', activate_el.value);
    }
    // 更新激活状态
    updateActiveState(nextVersion);

    // 更新按钮状态
    updateButtonState();

    // 更新 localStorage
    try {
      localStorage.setItem(
        'template_version_history',
        JSON.stringify({
          history: versionHistory.value,
          currentIndex: currentVersionIndex.value,
        }),
      );
    } catch (e) {
      console.warn('Failed to update version history:', e);
    }
    setTimeout(() => {
      isRevokeAndRecovery.value = false;
    }, 500);
  };

  // 初始化版本历史
  const initVersionHistory = () => {
    try {
      const savedVersion = localStorage.getItem('template_version_history');
      if (savedVersion) {
        const { history, currentIndex } = JSON.parse(savedVersion);
        versionHistory.value = history;
        currentVersionIndex.value = currentIndex;
        updateButtonState();
      }
    } catch (e) {
      // 如果加载失败,重置版本历史
      versionHistory.value = [];
      currentVersionIndex.value = -1;
      updateButtonState();
    }
  };

  // 图形显示
  const shapeImg = (eleData: string | number) => {
    let defaultImg = shapeImgConfig.value[1];
    let type = +eleData.shapeType;
    if (shapeImgConfig.value[type]) {
      defaultImg = shapeImgConfig.value[type];
    }
    return defaultImg;
  };

  // 创建 Web Worker
  const dataParserWorker = new Worker(
    new URL('../workers/dataParser.worker.ts', import.meta.url),
    {
      type: 'module',
    },
  );

  const fetchTemplate = async (id: string) => {
    try {
      loading.value = true;
      error.value = null;
      const response = await getTemplateInfo(id);
      if (response.status != 1) {
        message.error(response.msg || '获取模板信息失败');
        return;
      }
      templateInfo.value = response.data;

      if (templateInfo.value?.tool) {
        const toolData = JSON.parse(templateInfo.value.tool);
        tool.value = toolData;
        let res_module = toolData.modules?.map((item, index) => {
          item.module_show = item.module_show == 'false' ? false : true; // 模块是否显示
          return item;
        });
        if (!res_module) {
          res_module = [
            {
              name: '产品展示',
              module_key: '1',
              module_show: true,
              boxes: Object.keys(toolData.box),
              setting: { platform: [], link: [] },
            },
          ];
          const __tool = toolData.box[Object.keys(toolData.box)[0]];
          __tool.box_show = true;
        }
        // 优化数据处理，减少深拷贝
        const _boxes = toolData.box || {};
        const _modules = res_module || [];
        const _elements = toolData.element || {};

        // 处理模块数据
        const _modules_new = _modules.reduce((acc, module) => {
          acc[module.module_key] = module;
          module.boxes.forEach((boxKey: string) => {
            if (_boxes[boxKey]) {
              _boxes[boxKey].module_key = module.module_key;
              _boxes[boxKey].id = boxKey;
            }
          });
          return acc;
        }, {});

        // 处理元素数据
        Object.values(_boxes).forEach((box: any) => {
          box.content?.forEach((elementKey: string) => {
            if (_elements[elementKey]) {
              _elements[elementKey].box_key = box.id;
            }
          });
          box.hotLinkList?.forEach((hotLinkKey: string) => {
            if (_elements[hotLinkKey]) {
              _elements[hotLinkKey].box_key = box.id;
            }
          });
        });

        // 过滤无效元素
        const validElements = Object.entries(_elements)
          .filter(([_, element]: [string, any]) => element.box_key)
          .reduce((acc, [key, value]) => {
            acc[key] = value;
            return acc;
          }, {});

        // 批量更新状态
        modules.value = _modules;
        modulesObj.value = _modules_new;
        boxes.value = _boxes;
        boxesKeys.value = Object.keys(_boxes);
        elements.value = validElements;

        // 设置激活状态
        if (_modules.length > 0) {
          activate_module.value = _modules[0];
          const firstBoxKey = _modules[0].boxes[0];
          if (firstBoxKey && _boxes[firstBoxKey]) {
            activate_box.value = {
              key: firstBoxKey,
              ..._boxes[firstBoxKey],
            };
            const firstElementKey = _boxes[firstBoxKey].content?.[0];
            if (firstElementKey && validElements[firstElementKey]) {
              activate_el.value = validElements[firstElementKey];
							console.log('fetchTemplate--设置激活状态--activate_el.value', activate_el.value);
            }
          }
        }

        // 更新展开状态
        if (_modules[0]?.module_key) {
          updateExpandedModules(_modules[0].module_key, true);
        }
        if (activate_box.value?.id) {
          updateExpandedBoxes(activate_box.value.id, true);
        }

        // 处理其他模板信息
        otherTemplateInfo(toolData);
      }
    } catch (err) {
      error.value =
        err instanceof Error ? err.message : 'Failed to fetch template';
    } finally {
      loading.value = false;
    }
  };
  // 基础数据加载
  const loadBasicTemplateInfo = (templateInfo: any) => {
    version_record.value = templateInfo.version_record;
    back_version_url.value = templateInfo.back_version_url;
    text_system_field.value = templateInfo.text_system_field || [];
  };

  // 字体数据加载
  const loadFontData = (templateInfo: any) => {
    const _font = templateInfo.apply_fonts;
    font.value = Object.keys(_font).map((item) => ({
      value: item,
      label: item,
    }));
  };

  // 预览数据加载
  const loadPreviewData = (toolData: any) => {
    previewData.value = {
      previewShop: toolData.previewData?.previewShop || '',
      product_id: toolData.previewData?.product_id || '',
    };

    if (
      toolData?.previewData &&
      toolData.previewData?.previewShop &&
      toolData.previewData?.product_id
    ) {
      previewDataCommon(
        toolData.previewData?.previewShop,
        toolData.previewData?.product_id,
      );
      previewData.value.previewShop = toolData.previewData?.previewShop;
      previewData.value.product_id = toolData.previewData?.product_id;
    } else {
      previewData.value.previewShop = shop_list.value.length
        ? shop_list.value[0]?.value
        : '';
      //有天猫店铺默认选择天猫店铺，无天猫店铺则默认选择第一个，无店铺可选则显示【可选择】
      shop_list.value.forEach((item) => {
        if (item.type == '天猫') {
          previewData.value.previewShop = item.value;
        }
      });
    }
  };

  // 优化后的otherTemplateInfo方法
  const otherTemplateInfo = async (toolData: any) => {
    try {
      // 立即加载基础数据
      loadBasicTemplateInfo(templateInfo.value);

      // 使用 Web Worker 处理数据解析
      return new Promise((resolve, reject) => {
        dataParserWorker.onmessage = (e) => {
          if (e.data.type === 'success') {
            const {
              shopList,
              platformList,
              publicImage,
              excelHotValue,
              text_zdy_sep_option,
              templateField,
              templateTitle,
            } = e.data.data;

            // 批量更新状态，减少响应式更新次数
            shop_list.value = shopList;
            platform_list.value = platformList;
            template_public_image.value = publicImage;
            template_excel_hot_value.value = excelHotValue;
            text_zdy_sep_opt.value = text_zdy_sep_option;
            template_field.value = templateField;
            template_title.value = templateTitle;
            // 加载字体数据
            loadFontData(templateInfo.value);

            // 加载预览数据
            loadPreviewData(toolData);

            resolve(undefined);
          } else {
            reject(new Error(e.data.error));
          }
        };

        // 确保发送给 worker 的数据是可序列化的
        const serializableData = JSON.parse(JSON.stringify(templateInfo.value));
        dataParserWorker.postMessage({
          type: 'parse',
          data: serializableData,
        });
      });
    } catch (error) {
      throw error;
    }
  };

  // 在组件卸载时清理 Worker 和节流函数
  onUnmounted(() => {
    dataParserWorker.terminate();
    saveVersion.cancel(); // 取消未执行的节流函数
  });

  // 自定义比较函数，忽略x和y字段
  const isEqualExceptXY = (obj1: any, obj2: any) => {
    if (!obj1 || !obj2) return obj1 === obj2;

    // 创建副本并删除x和y字段
    const copy1 = { ...obj1 };
    const copy2 = { ...obj2 };
    delete copy1.x;
    delete copy1.y;
    delete copy2.x;
    delete copy2.y;

    return isEqual(copy1, copy2);
  };

  //右侧改变通知elements
  watch(
    () => activate_el.value,
    (n, o) => {
      if (!n) return;
			if (isEqualExceptXY(n, o)) {
			  return;
			}
			console.log('右侧改变通知elements',n)
      if (n.element_id != o?.element_id) {
        //右侧需要滚动到最上面
        scrollToAttributeTop();
      }

      if (n.type == '1' && n.bg_color_set !== '4') {
        n.bg_img_gradation_color1 = '#ffffff';
        n.bg_img_gradation_config1 = 0;
        n.bg_img_gradation_color2 = '#ffffff';
        n.bg_img_gradation_config2 = 0;
        n.bg_img_gradation_color3 = '#ffffff';
        n.bg_img_gradation_config3 = 0;
      } else if (n.type == '1' && n.bg_color_set == '4') {
        n.bg_color = '#ffffff';
      }
      // 创建新的elements对象以确保触发响应式更新
      const newElements = { ...elements.value };
      newElements[n.element_id] = { ...n };
      elements.value = newElements;

      // 添加版本保存
      if (!isPreview.value && !isRevokeAndRecovery.value) {
        console.log('saveVersion--- watch--activate_el');
        saveVersion();
      }
    },
    { deep: true },
  );

  //active_box改变通知boxes
  watch(
    () => activate_box.value,
    (n, o) => {
      if (n && n.id) {
        boxes.value[n.id] = n;
      }
      // 不处于预览和撤销恢复状态,自动保存版本
      if (!isPreview.value && !isRevokeAndRecovery.value) {
        saveVersion();
      }
    },
    { deep: true },
  );
  //module_box改变通知modules
  watch(
    () => activate_module.value,
    (n, o) => {
      modules.value.map((item) => {
        if (item.module_key === n.module_key) {
          return n;
        } else {
          return item;
        }
      });
    },
    { deep: true },
  );
  // 打开预览链接
  const openPreviewLink = () => {
    if (sales_brand.value) {
      let url =
        import.meta.env.VITE_GLOB_API_URL +
        'previewGoodsTemplateDetail?sales_brand=' +
        sales_brand.value +
        '&id=' +
        previewId.value +
        '&template_id=' +
        templateInfo.value.template_id;
      window.open(url);
    } else {
      message.error('请先设置预览链接');
    }
  };
  // 预览
  const getPreview = () => {
    if (previewData.value?.previewShop && previewData.value?.product_id) {
      loading.value = true;
      previewDataCommon(
        previewData.value?.previewShop,
        previewData.value?.product_id,
      );
    } else {
      message.error('请先设置预览链接');
    }
  };
  const previewDataCommon = async (previewShop: string, productId: string) => {
    isPreview.value = true; // 表示正在设置预览数据，防止触发撤销恢复逻辑
    let prams = {
      action_shop_page_id: action_shop_page_id.value,
      shop_id: previewShop,
      num_iid: productId,
      tool: JSON.stringify({
        element: elements.value,
        box: boxes.value,
        modules: modules.value,
        version: version.value,
      }),
    };

    let res = await getPreviewData(prams);
    if (res?.status === 1) {
      previewData.value.previewShop = previewShop;
      previewData.value.product_id = productId;
      Modal.destroyAll();
      let resElement = res?.data?.element || [];
      let old_element = JSON.parse(JSON.stringify(elements.value));
      for (const key in old_element) {
        if (resElement[key]) {
          let resItemel = resElement[key];
          if (old_element[key].type === '1') {
            if (resItemel.memoryImgPath) {
              old_element[key].memoryImgPath = resItemel.memoryImgPath;
            }
            if (resItemel.local_path) {
              old_element[key].memoryImgPath = resItemel.local_path;
            }
          } else if (old_element[key].type === '2' && resItemel.text_content) {
            old_element[key].defaultText = resItemel.text_content;
          }
        }
      }
      setAllElements(old_element);
      sales_brand.value = res?.data?.sales_brand;
      previewId.value = res?.data?.goods_info?.id;
      setTimeout(() => {
        isPreview.value = false; // 设置预览数据结束
        loading.value = false;
      }, 1000);
    } else {
      message.error(res.msg || '获取预览数据失败');
      if (!productId) {
        // 当设置预览链接，没有商品ID时，点击确定后，清空预览数据
        clearPreview();
      }
      previewData.value.previewShop = '';
      previewData.value.product_id = '';
      Modal.destroyAll();
      isPreview.value = false; // 设置预览数据结束
      loading.value = false;
    }
  };
  const clearPreview = () => {
    isPreview.value = true; // 表示正在设置预览数据，防止触发撤销恢复逻辑
    // layer.load(2, { shade: [0.3, "#393D49"], time: 1000 });
    previewData.value.previewShop = '';
    previewData.value.product_id = '';
    let appElement = JSON.parse(JSON.stringify(elements.value));
    for (const key in appElement) {
      if (appElement[key].type === '1') {
        appElement[key].memoryImgPath = null;
      } else if (appElement[key].type === '2') {
        appElement[key].defaultText = null;
      }
    }
    setAllElements(appElement);
    isPreview.value = false;
  };
  // 添加新的方法
  const updateElement = (elementKey: string, newData: Partial<ElementType>) => {
    if (elements.value && elements.value[elementKey]) {
      elements.value[elementKey] = {
        ...elements.value[elementKey],
        ...newData,
      };
    }
  };

  const updateModule = (moduleKey: string, newData: Partial<ModuleType>) => {
    const moduleIndex = modules.value.findIndex(
      (m) => m.module_key === moduleKey,
    );
    if (moduleIndex !== -1) {
      modules.value[moduleIndex] = {
        ...modules.value[moduleIndex],
        ...newData,
      };
    }
  };

  const setActiveBox = (box: BoxType, module: ModuleType | null = null) => {
		console.log('setActiveBox', box, module,activate_box.value.id==box.id,activate_box.value.id,box.id);
		if(activate_box.value.id==box.id) return;
    // 如果active_el不是这个box的content里的,则要选中第一个active_el // 热区逻辑例外
    if (
			activate_box.value.id!=box.id&&(
      (activate_el.value?.type != 'HOT_LINK' &&
        !box?.content?.includes(activate_el.value?.element_id || '')) ||
      (activate_el.value?.type == 'HOT_LINK' &&
        box.id !== activate_el.value?.box_id))
    ) {
			console.log('setActiveBox--选中第一个active_el', box, module,activate_el);
      setActiveElement(
        elements.value[activate_el.value?.element_id],
        box.id,
        module,
      );
    }
		console.log('setActiveBox', box, module);
		activate_box.value = box;
		scrollToActiveBox();
    updateExpandedBoxes(box.id, true);
    // console.log('setActiveBox', box, module);
    // 更新激活module
    if (module && module.module_key != activate_module.value?.module_key) {
      setActiveModule(module);
    } else {
      modules.value.forEach((module) => {
        if (module.boxes?.includes(box.id)) {
          setActiveModule(module);
        }
      });
    }

    // 判断当前激活的box是否有激活的元素,处理菜单
    let flag = true;
    if (!activate_el.value || !activate_el.value.element_id) {
      // If no active_el or its element_id is missing, it's not in the current box.
      flag = false;
    } else {
      // activate_el.value and activate_el.value.element_id are valid.
      // Ensure activate_box.value is not null before accessing its properties.
      if (activate_box.value) {
        flag =
          (activate_box.value.content?.includes(activate_el.value.element_id) ??
            false) ||
          (activate_box.value.hotLinkList?.includes(
            activate_el.value.element_id,
          ) ??
            false);
      } else {
        // If activate_box is null, then the element cannot be in it.
        flag = false;
      }
    }

    if (!flag) {
      const allTypes = Object.values(typeMap.value);
      // 重置所有相关 key 为 false
      allTypes.forEach((t) => {
        // Use activate_box.value.id and ensure activate_box.value is not null
        if (activate_box.value) {
          expandedElements.value[`${activate_box.value.id}-${t}`] = false;
        }
      });
    }
    
  };

  // 设置当前激活的元素
  const setActiveElement = (
    element: ElementType | null,
    boxKey: string,
    module: ModuleType | null = null,
  ) => {
    if (activate_el.value?.element_id != element?.element_id) {
      activate_el.value = { ...activate_el.value, ...element };
			console.log('setActiveElement--设置当前激活的元素--activate_el.value', activate_el.value);
			
      // 更新激活的画布 如果画布不是当前的画布 则修改box
      if (activate_box.value.id != boxKey) {
        setActiveBox({ key: boxKey, ...boxes.value[boxKey] }, module);
      }
      setExpandedElement(boxKey, element?.type);
    }
  };
  // 添加元素
  const addElement = (element: ElementType, boxKey: string, isDrop: string) => {
    let parse_boxes = JSON.parse(JSON.stringify(boxes.value));
    let parse_elements = JSON.parse(JSON.stringify(elements.value));
    let uuid = guid2('EL');
    if (element.type == 'HOT_LINK') {
      if (!parse_boxes[boxKey].hasOwnProperty('hotLinkList')) {
        parse_boxes[boxKey].hotLinkList = [];
      }
      uuid = guid2('LINK');
      element.element_id = uuid;
      parse_elements[uuid] = JSON.parse(JSON.stringify(element));
      parse_boxes[boxKey].hotLinkList
        ? parse_boxes[boxKey].hotLinkList.push(uuid)
        : (parse_boxes[boxKey].hotLinkList = [uuid]);
    } else {
      element.element_id = uuid;
      parse_elements[uuid] = element;
      parse_boxes[boxKey].content
        ? parse_boxes[boxKey].content.push(uuid)
        : (parse_boxes[boxKey].content = [uuid]);
    }
    setAllBoxes(parse_boxes);
    setAllElements(parse_elements);
    if (isDrop == 'drop' && element.type == 'SHAPE') {
      setActiveElement({ key: uuid, ...element }, boxKey);
    }
  };
  // 设置当前激活的元素的展开状态
  const setExpandedElement = (boxKey: string, elementType: string) => {
    const allTypes = Object.values(typeMap.value);
    const currentType = typeMap.value[elementType] || '';
    // 重置所有相关 key 为 false
    allTypes.forEach((t) => {
      expandedElements.value[`${boxKey}-${t}`] = false;
    });

    // 设置当前类型为 true
    if (currentType) {
      expandedElements.value[`${boxKey}-${currentType}`] = true;
    }
  };
  // 设置当前激活的模块
  const setActiveModule = (module: ModuleType | null) => {
    activate_module.value = { ...module };
    updateExpandedModules(module?.module_key, true);
  };
  const setAllElements = (newElements: any) => {
    elements.value = newElements;
  };
  const setAllBoxes = (newBoxes: any) => {
    boxes.value = newBoxes;
    boxesKeys.value = Object.keys(newBoxes);
  };
  // 设置modules
  const setAllModules = (newModules: any) => {
    modules.value = [...newModules];
  };

  // 保存模板
  const saveTemplate = () => {
    // 数据清洗
    //遍历所有modules,如果boxes中没有就更新modules的boxes
    modules?.value?.forEach((module) => {
      module?.boxes?.forEach((box) => {
        if (!boxes.value[box]) {
          module.boxes = module.boxes?.filter((item) => item != box);
        }
      });
      module.module_show = module.module_show.toString();
    });
    // 删除boxes的copy_object
    Object.keys(boxes.value).forEach((key) => {
      delete boxes.value[key].copy_object;
      delete boxes.value[key].value;
      boxes.value[key].box_show = boxes.value[key]?.box_show.toString();
    });
    //删除element中的value
    Object.keys(elements.value).forEach((key) => {
      delete elements.value[key].value;
      //如果有的element没有height,则补充和imgHeight字段一样
      if (!elements.value[key].type == 1) {
        if (!elements.value[key].height) {
          elements.value[key].height = Number(elements.value[key].imgHeight);
        }
        if (!elements.value[key].width) {
          elements.value[key].width = Number(elements.value[key]?.imgWidth);
        }
        if (!elements.value[key].imgHeight) {
          elements.value[key].imgHeight = Number(elements.value[key]?.height);
        }
        if (!elements.value[key].imgWidth) {
          elements.value[key].imgWidth = Number(elements.value[key]?.width);
        }
      }
    });

    const save_tool = {
      box: boxes.value,
      modules: modules.value,
      element: elements.value,
      version: version.value,
      previewData: {
        previewShop: previewData.value?.previewShop,
        product_id: previewData.value?.product_id,
      },
    };
    let param = {
      _token: authStore.token,
      id: id.value,
      temp_name: templateInfo.value?.template_name,
      tool: save_tool,
    };
    console.log('saveTemplate', param);

    saveTemplateApi(param).then((res) => {
      message.success('保存成功');
      window.location.reload();
    });
  };
  // 备份模板
  const handleBackup = (data: any) => {
    console.log('备份模板', {
      ...data,
      template_id: templateInfo.value.template_id,
      action_shop_page_id: router.currentRoute.value.query.action_shop_page_id,
    });
    commitTemplateBackup({
      ...data,
      template_id: templateInfo.value.template_id,
    }).then((res) => {
      console.log('res', res);
      message.success('备份成功');
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    });
  };
  const updateExpandedModules = (module_key: string, flag: boolean) => {
    expandedModules.value[module_key] = flag;
    if (flag) {
      expandedModules.value = Object.keys(expandedModules.value).reduce(
        (acc, key) => {
          acc[key] = key === module_key; // 当前 key 等于 module_key 则设为 true，否则 false
          return acc;
        },
        {},
      );
    }
  };
  const updateExpandedBoxes = (box_key: string, flag: boolean) => {
    expandedBoxes.value[box_key] = flag;
    if (flag) {
      expandedBoxes.value = Object.keys(expandedBoxes.value).reduce(
        (acc, key) => {
          acc[key] = key === box_key; // 当前 key 等于 module_key 则设为 true，否则 false
          return acc;
        },
        {},
      );
    }
  };
  const updateExpandedElements = (element_key: string, flag: boolean) => {
    expandedElements.value[element_key] = flag;
  };
  const guid2 = (t = '') => {
    function S4() {
      return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
    }
    return t + (S4() + S4() + S4() + S4() + S4() + S4() + S4() + S4());
  };
  // 添加画布
  const addBox = (
    id: string,
    moduleLastBoxKey: string,
    moduleKey: string,
    operaBox: string = 'addBox',
  ) => {
    console.log('新增画布', id, moduleLastBoxKey);
    /**
     * @id 新增画布id
     * @param {string} moduleLastBoxKey 当前active_module的最后一块画布的key
     * @description 新增画布
     */
    let act_box = JSON.parse(JSON.stringify(activate_box.value)); //深克隆
    delete act_box.merge_box;
    delete act_box.merge;
    delete act_box.formula_key;
    delete act_box.formula_type;
    delete act_box.formula_type2;
    delete act_box.formula_value;
    act_box.bg_color = null;
    act_box.name = '';
    act_box.height = 1500; //画布高度默认1500
    act_box.content = [];
    act_box.hotLinkList = [];
    act_box.remark = ''; // 备注
    act_box.box_show = true; //是否显示
    act_box.conditional_relation = '1'; //多条件关系
    act_box.show_relation_filter = '';
    act_box.relation_filter_arr = [
      {
        filterObj: {},
        formula_type: '1',
        formula_type2: '1',
        value_relation: '1',
      },
    ];
    act_box.relation_filter_verify = [
      {
        filterObj: {},
        formula_type: '1',
        formula_type2: '1',
        value_relation: '1',
      },
    ];
    act_box.formula_name_type = '0';
    let uuid = id || guid2('BOX');
    let new_box = {};
    for (let i in boxes.value) {
      new_box[i] = boxes.value[i];
      if (moduleLastBoxKey) {
        //新增模块自动添加的画布
        if (i == moduleLastBoxKey) {
          new_box[uuid] = JSON.parse(
            JSON.stringify({ ...act_box, key: uuid, id: uuid }),
          );
        }
      } else {
        console.log('else---moduleLastBoxKey', uuid);
        //手动点击添加画布
        if (i == act_box.id) {
          new_box[uuid] = JSON.parse(
            JSON.stringify({ ...act_box, key: uuid, id: uuid }),
          );
        }
      }
    }
    // console.log('new_box',JSON.stringify(new_box));

    setAllBoxes(new_box);
    // 处理模块数据
    dealModulesData(uuid, operaBox, moduleKey);
  };
  // 删除画布
  const delBox = () => {
    Modal.confirm({
      title: () => '删除画布',
      icon: () => createVNode(ExclamationCircleOutlined),
      content: createVNode('div', { style: {} }, [
        createVNode('p', { style: { color: '#999' } }, '确定删除当前画布吗？'),
      ]),
      onOk() {
        if (!activate_box.value) {
          message.error('没有激活的画布');
          return;
        }
        const boxKey = activate_box.value.id;
        const _boxes = JSON.parse(JSON.stringify(boxes.value));
        const _modules = JSON.parse(JSON.stringify(modules.value));
        const content = _boxes[boxKey]?.content;
        const hotLinkList = _boxes[boxKey]?.hotLinkList;
        const _elements = JSON.parse(JSON.stringify(elements.value));

        let key_index = '';
        let keys = Object.keys(_boxes);
        let box_length = keys.length;
        //手动删除画布时，判断画布是否是该模块唯一
        const module_only_box = _modules.filter((element) => {
          return element.boxes.includes(boxKey) && element.boxes.length == 1;
        });
        if (module_only_box.length == 1)
          return message.error('该画布是模块唯一画布，不能删除');
        if (box_length <= 1) {
          message.error('最后一个画布不能删除');
          return false;
        }
        for (let i in keys) {
          if (keys[i] == boxKey) {
            key_index = i;
          }
        }
        for (let i in content) {
          if (_elements[content[i]]) {
            delete _elements[content[i]];
          }
        }
        for (let y in hotLinkList) {
          if (_elements[hotLinkList[y]]) {
            delete _elements[hotLinkList[y]];
          }
        }
        //删除当前
        delete _boxes[boxKey];
        setAllElements(_elements);
        setAllBoxes(_boxes);
        // 处理module数据
        dealModulesData(boxKey, 'delBox', '');
      },
      onCancel() {
        console.log('Cancel');
      },
      class: 'test',
    });
  };
  // 画布移动
  const moveBox = (type: string) => {
    const boxKey = activate_box.value?.id;
    if (!boxKey) {
      message.error('没有激活的画布');
      return;
    }
    const _boxes = JSON.parse(JSON.stringify(boxes.value));
    const _modules = JSON.parse(JSON.stringify(modules.value));
    const taeget_module = _modules.filter((item) =>
      item.boxes.includes(boxKey),
    )[0];
    const module_key = taeget_module.module_key;
    const first_box_key = taeget_module.boxes[0];
    const last_box_key = taeget_module.boxes[taeget_module.boxes.length - 1];
    let key_index = '';
    let keys = Object.keys(_boxes);
    let box_length = parseInt(keys.length);
    for (let i in keys) {
      if (keys[i] == boxKey) {
        key_index = i;
      }
    }
    if (type == 'up' && first_box_key == boxKey) {
      return message.info('已是该模块的第一个画布');
    }
    if (
      box_length == 1 ||
      (key_index == 0 && type == 'up') ||
      (key_index == box_length - 1 && type == 'down')
    ) {
      return false;
    }
    if (type == 'down' && last_box_key == boxKey) {
      return message.info('已是该模块的最后一个画布');
    }
    if (type == 'up') {
      const cp_key = keys[parseInt(key_index) - 1];
      keys[parseInt(key_index) - 1] = boxKey;
      keys[key_index] = cp_key;
    } else if (type == 'down') {
      const cp_key = keys[parseInt(key_index) + 1];
      keys[parseInt(key_index) + 1] = boxKey;
      keys[key_index] = cp_key;
    }
    let new_box = {};
    for (let i in keys) {
      new_box[keys[i]] = _boxes[keys[i]];
    }
    setAllBoxes(JSON.parse(JSON.stringify(new_box)));
    scrollToActiveBox();
    // 移动画布，处理画布key在modules的位置
    moveModuleBox(module_key, boxKey, type, _modules);
  };
  // 滚动到激活的画布位置
  const scrollToActiveBox = () => {
    nextTick(() => {
      // 滚动到.active_box画布位置
      const canvasContainer = document.querySelector('.canvas-container');
      const activeBox = document.querySelector('.active_box');
      const activeEl = document.querySelector('.active_el');

      if (canvasContainer && activeEl && activeBox) {
        const offset =
          activeBox.offsetTop - canvasContainer.offsetTop + activeEl.offsetTop;
        canvasContainer.scrollTo({
          top: offset,
          behavior: 'smooth',
        });
      }
    });
  };
  // 滚动到配置的最上方
  const scrollToAttributeTop = () => {
    nextTick(() => {
      // 滚动到.active_box画布位置
      const canvasContainer = document.querySelector(
        '.component-attribute .attribute-comp',
      );
      console.log('canvasContainer', canvasContainer);
      if (canvasContainer) {
        canvasContainer.scrollTo({
          top: 0,
          behavior: 'smooth',
        });
      }
    });
  };
  const moveModuleBox = (
    moduleKey: string,
    boxKey: string,
    type: string,
    modules: any,
  ) => {
    /**
     * 移动画布，找到该box_key在module.boxes中的位置，前后移动这个位置
     * moduleKey: 模块key
     * boxKey: 画布key
     * type: up:上移, down：下移
     * modules: 模块数据
     */
    let new_module = modules;
    const targetModule = modules.find(
      (module) => module.module_key === moduleKey,
    );
    if (targetModule) {
      const boxIndex = targetModule.boxes.indexOf(boxKey);
      if (type === 'up' && boxIndex > 0) {
        const temp = targetModule.boxes[boxIndex];
        targetModule.boxes[boxIndex] = targetModule.boxes[boxIndex - 1];
        targetModule.boxes[boxIndex - 1] = temp;
      } else if (type === 'down' && boxIndex < targetModule.boxes.length - 1) {
        const temp = targetModule.boxes[boxIndex];
        targetModule.boxes[boxIndex] = targetModule.boxes[boxIndex + 1];
        targetModule.boxes[boxIndex + 1] = temp;
      }
    }
    new_module = modules.map((module) => {
      if (module.module_key === moduleKey) {
        return { ...module, boxes: targetModule.boxes };
      }
      return module;
    });
    setAllModules(new_module);
  };
  // 处理模块数据新增/删除画布时更新modules数据
  const dealModulesData = (
    uuid: string,
    operaBox: string,
    moduleKey: string,
  ) => {
    /**
     * @param {string} uuid 新增的画布key
     * @param {string} operaBox 操作类型 addBox新增 delBox删除 addModuleBox点击模块新增画布
     * @param {string} moduleKey 模块key, 有值时：表示点击模块上的新增画布
     * @description 新增/删除画布时处理modules数据
     */
    let act_box = JSON.parse(JSON.stringify(activate_box.value)); //深克隆
    let _modules = modules.value;
    let active_box_key = act_box.id;

    if (moduleKey) {
      let act_module = _modules.filter((element) => {
        return element.module_key == moduleKey;
      });
      active_box_key = act_module[0].boxes[act_module[0]?.boxes?.length - 1];
      console.log('moduleKey', moduleKey, active_box_key);
    }
    let new_module = [];
    for (let item = 0; item < _modules.length; item++) {
      if (_modules[item].boxes.indexOf(active_box_key) !== -1) {
        // 创建一个完全独立的深拷贝，避免引用污染
        let _boxKeys = JSON.parse(JSON.stringify(_modules[item].boxes));
        if (operaBox === 'addBox') {
          console.log('变更之前的_boxKeys', JSON.stringify(_boxKeys));
          // 向当前activate_box的后面一个位置插入元素
          _boxKeys.splice(
            _modules[item].boxes.indexOf(active_box_key) + 1,
            0,
            uuid,
          );
          console.log('变更之后的_boxKeys', JSON.stringify(_boxKeys));
        } else if (operaBox === 'delBox') {
          _boxKeys.splice(_boxKeys.indexOf(active_box_key), 1);
        }
        console.log(
          '打印出每次push的_boxKeys',
          JSON.stringify({
            name: _modules[item].name,
            module_key: _modules[item].module_key,
            module_show: _modules[item].module_show,
            boxes: [..._boxKeys],
            setting: _modules[item].setting,
          }),
        );

        // 使用深拷贝创建新的模块对象，避免引用原始对象
        new_module.push(
          JSON.parse(
            JSON.stringify({
              name: _modules[item].name,
              module_key: _modules[item].module_key,
              module_show: _modules[item].module_show,
              boxes: [..._boxKeys],
              setting: _modules[item].setting,
            }),
          ),
        );
      } else {
        // 对未修改的模块也进行深拷贝
        new_module.push(JSON.parse(JSON.stringify(_modules[item])));
      }
    }
    console.log('new_module???????', JSON.stringify(new_module));

    setAllModules(new_module);
  };
  // 删除元素
  const delElement = (boxKey: string, elKey: string) => {
    let temp_box = JSON.parse(JSON.stringify(boxes.value));
    let temp_element = JSON.parse(JSON.stringify(elements.value));
    let _activate_box = JSON.parse(JSON.stringify(activate_box.value));
    const box = temp_box[boxKey];
    const el = temp_element[elKey];
    if (el.type == 'HOT_LINK') {
      let _index = box.hotLinkList.indexOf(elKey);
      box.hotLinkList.splice(_index, 1);
      _activate_box.hotLinkList.splice(_index, 1);
    } else {
      let _index = box.content.indexOf(elKey);
      box.content.splice(_index, 1);
      _activate_box.content.splice(_index, 1);
    }
    delete temp_element[elKey];
		console.log('测试卡步骤1', JSON.stringify(temp_element));
    setActiveBox(_activate_box);
		console.log('测试卡步骤2', JSON.stringify(temp_element));
    setAllElements(temp_element);
		console.log('测试卡步骤3', JSON.stringify(temp_element));
    setAllBoxes(temp_box);
		console.log('测试卡步骤4', JSON.stringify(temp_element));
  };
  // 新增模块函数
  const addModule = (new_module_name: string) => {
    let moduleUuid = guid2('MODULE');
    let uuid = guid2('BOX');
    let _modules = modules.value;
    if (!activate_box.value) {
      message.error('没有激活的画布用于参考模块位置');
      return; // Or handle differently, e.g., add to the very end
    }
    let active_Module_box_key = activate_box.value.id; // Use .id
    console.log(JSON.stringify(_modules));

    active_Module_box_key = findModuleLastBoxKey(
      _modules,
      _modules[_modules.length - 1].boxes[0],
    );
    console.log('排查第一步', active_Module_box_key, JSON.stringify(_modules));

    _modules.push({
      name: new_module_name,
      module_key: moduleUuid,
      module_show: true,
      boxes: [uuid],
      setting: { platform: [], link: [] },
    });
    console.log('排查第二步', JSON.stringify(_modules));

    setAllModules(_modules);
    nextTick(() => {
      addBox(uuid, active_Module_box_key, '', 'addModuleBox');
    });
  };
  // 模块删除
  const delModule = (moduleKey: string) => {
    Modal.confirm({
      title: () => '确定删除当前模块吗？',
      icon: () => createVNode(ExclamationCircleOutlined),
      content: createVNode('div', { style: {} }, [
        createVNode(
          'p',
          { style: { color: '#999' } },
          '删除之后将无法生成该模块，防止数据丢失可以先进行备份！',
        ),
      ]),
      onOk() {
        const _modules = [...modules.value];
        if (_modules.length == 1)
          return message.error('最后一个模块,不可删除!');
        const index = _modules.findIndex((m) => m.module_key === moduleKey);
        if (index === -1) return;
        let target_del_box = _modules[index].boxes;
        _modules.splice(index, 1);
        const newBoxes = { ...boxes.value };
        target_del_box.forEach((boxKey) => {
          delete newBoxes[boxKey];
        });
        // 更新 modules boxes
        setAllBoxes(newBoxes);
        setAllModules(_modules);
        setActiveModule(_modules[0]);
        setActiveBox({
          ...boxes.value[_modules[0].boxes[0]],
          key: _modules[0].boxes[0],
        });
      },
      onCancel() {
        console.log('Cancel');
      },
      class: 'test',
    });
  };
  //编辑模块名称函数
  const editModule = (new_module_name: string, moduleKey: string) => {
    const new_module = modules.value.map((module) => {
      if (module.module_key == moduleKey) {
        module.name = new_module_name;
      }
      return module;
    });
    setActiveModule({ ...activate_module.value, name: new_module_name });
    saveVersion(); //添加版本保存
  };
  // 复制模块函数
  const copyModule = (new_module_name: string, moduleKey: string) => {
    let _elements = elements.value;
    let _modules = modules.value;
    let _boxes = boxes.value;
    let copyBox = [];
    let new_boxes = {};
    const new_module_key = guid2('MODULE');
    const targetModuleIndex = _modules.findIndex(
      (module) => module.module_key === moduleKey,
    );
    if (targetModuleIndex !== -1) {
      const targetModule = JSON.parse(
        JSON.stringify(_modules[targetModuleIndex]),
      );
      const newBoxes = targetModule.boxes.map(() => guid2('BOX'));
      const newModule = {
        name: new_module_name,
        module_key: new_module_key,
        module_show: targetModule.module_show, // 是否显示，和被复制模块保持一致
        boxes: newBoxes,
        setting: targetModule.setting,
      };
      _modules.splice(targetModuleIndex + 1, 0, newModule);
      // 目标module的最后一块画布的key
      const lastBoxKey = targetModule.boxes[targetModule.boxes.length - 1];
      for (let i in _boxes) {
        for (let j = 0; j < targetModule.boxes.length; j++) {
          if (i == targetModule.boxes[j]) {
            copyBox.push({
              key: newBoxes[j],
              id: newBoxes[j],
              value: { ..._boxes[i], key: newBoxes[j], id: newBoxes[j] },
              content: _boxes[i].content || [],
              hotLinkList: _boxes[i].hotLinkList || [],
            });
          }
        }
      }
      const newElement = {};
      const oldNewKeyMap = {}; // 用于存储旧key到新key的映射
      copyBox = JSON.parse(JSON.stringify(copyBox)).map((item) => {
        const newContent = item.content?.map((contentKey) => {
          if (_elements[contentKey]) {
            let new_randomString = guid2('EL');
            oldNewKeyMap[contentKey] = new_randomString;
            newElement[new_randomString] = {
              ..._elements[contentKey],
              element_id: new_randomString,
            };
            return new_randomString;
          }
          return contentKey;
        });
        const newHotLinkList = item.hotLinkList?.map((linkKey) => {
          if (_elements[linkKey]) {
            let new_randomString = guid2('LINK');
            oldNewKeyMap[linkKey] = new_randomString;
            newElement[new_randomString] = {
              ..._elements[linkKey],
              element_id: new_randomString,
            };
            return new_randomString;
          }
          return linkKey;
        });
        return {
          ...item,
          content: newContent,
          hotLinkList: newHotLinkList,
        };
      });
      for (let i in _boxes) {
        new_boxes[i] = JSON.parse(JSON.stringify(_boxes[i]));
        if (lastBoxKey == i) {
          copyBox.forEach((copy) => {
            new_boxes[copy.key] = JSON.parse(
              JSON.stringify({
                ...copy.value,
                content: copy.content,
                hotLinkList: copy.hotLinkList,
              }),
            );
          });
        }
      }
      // 根据新旧的key值映射，更新bind_el，relative_key_x和relative_key_y和follow_show的值
      for (let elementId in newElement) {
        let elementData = newElement[elementId];
        if (elementData.hasOwnProperty('follow_show')) {
          let newFollow = [];
          if (elementData.follow_show) {
            if (
              elementData.follow_show.includes('|') &&
              elementData.follow_show.split('|').length > 1
            ) {
              // 跟随显示，多选，|分割
              elementData.follow_show.split('|').forEach((follow) => {
                if (oldNewKeyMap.hasOwnProperty(follow)) {
                  newFollow.push(oldNewKeyMap[follow]);
                }
              });
              elementData.follow_show = newFollow.join('|');
            } else if (
              elementData.follow_show.includes('&') &&
              elementData.follow_show.split('&').length > 1
            ) {
              // 跟随显示，多选，&分割
              elementData.follow_show.split('&').forEach((follow) => {
                if (oldNewKeyMap.hasOwnProperty(follow)) {
                  newFollow.push(oldNewKeyMap[follow]);
                }
              });
              elementData.follow_show = newFollow.join('&');
            } else {
              // 跟随显示，单选
              elementData.follow_show = oldNewKeyMap[elementData.follow_show];
            }
          }
        }
        // 垂直维度
        if (elementData.hasOwnProperty('relative_key_y')) {
          let newRelativeKeyY = [];
          if (elementData.relative_key_y) {
            if (
              elementData.relative_key_y.includes(',') &&
              elementData.relative_key_y.split(',').length > 1
            ) {
              // 跟随显示，多选，|分割
              elementData.relative_key_y.split(',').forEach((relativeY) => {
                if (oldNewKeyMap.hasOwnProperty(relativeY)) {
                  newRelativeKeyY.push(oldNewKeyMap[relativeY]);
                }
              });
              elementData.relative_key_y = newRelativeKeyY.join(',');
            } else {
              // 垂直维度，单选
              elementData.relative_key_y =
                oldNewKeyMap[elementData.relative_key_y];
            }
          }
        }
      }

      const updateElementKeys = (obj) => {
        if (!obj || typeof obj !== 'object') return obj;

        // 如果是数组，递归处理每个元素
        if (Array.isArray(obj)) {
          return obj.map((item) => updateElementKeys(item));
        }

        // 如果是对象，处理每个属性
        const newObj = {};
        for (const [key, value] of Object.entries(obj)) {
          if (
            typeof value === 'string' &&
            (value.startsWith('EL') || value.startsWith('LINK'))
          ) {
            // 如果值是元素key，则更新为新key
            newObj[key] = oldNewKeyMap[value] || value;
          } else if (typeof value === 'object' && value !== null) {
            // 递归处理对象类型的值
            newObj[key] = updateElementKeys(value);
          } else {
            // 其他类型的值保持不变
            newObj[key] = value;
          }
        }
        return newObj;
      };

      // 3. 更新所有元素中的key引用
      for (let elementId in newElement) {
        newElement[elementId] = updateElementKeys(newElement[elementId]);
      }

      setAllModules(JSON.parse(JSON.stringify(_modules)));
      setAllBoxes(JSON.parse(JSON.stringify(new_boxes)));
      setAllElements(
        JSON.parse(JSON.stringify({ ..._elements, ...newElement })),
      );
    }
  };
  // 模块移动
  const moduleMove = (moduleKey: string, direction: 'down' | 'up') => {
    let _modules = [...modules.value];
    const index = _modules.findIndex((m) => m.module_key === moduleKey);
    if (index === -1) return;

    const newIndex = direction === 'up' ? index - 1 : index + 1;
    if (newIndex < 0 || newIndex >= _modules.length) return;

    [_modules[index], _modules[newIndex]] = [
      _modules[newIndex],
      _modules[index],
    ];
    const sortedBoxes = {};
    _modules.forEach((module) => {
      module.boxes.forEach((boxKey) => {
        sortedBoxes[boxKey] = boxes.value[boxKey];
      });
    });
    // 更新 modules boxes
    setAllBoxes(sortedBoxes);
    setAllModules(_modules);
  };
  // 控制模块菜单是否显示/隐藏
  const controlModule = (moduleKey: string, show: boolean) => {
    const moduleIndex = modules.value.findIndex(
      (m) => m.module_key === moduleKey,
    );
    if (moduleIndex !== -1) {
      const updatedModule = {
        ...modules.value[moduleIndex],
        module_show: show,
      };

      updateModule(moduleKey, updatedModule);

      // 如果隐藏模块，则取消展开状态
      if (!show) {
        updateExpandedModules(moduleKey, false);
      } else if (activate_module.value?.module_key === moduleKey) {
        // 如果显示模块且是当前激活模块，则展开
        updateExpandedModules(moduleKey, true);
      }

      // 更新该模块下所有画布的显示状态
      const newBoxes = { ...boxes.value };
      updatedModule.boxes.forEach((boxKey) => {
        if (newBoxes[boxKey]) {
          newBoxes[boxKey].box_show = show;
        }
      });
      setAllBoxes(newBoxes);
    }
  };
  // 点击模块上的添加画布
  const addModuleBox = (moduleKey: string) => {
    const _modules = JSON.parse(JSON.stringify(modules.value));
    const targetModule = _modules.find((module) => {
      return module.module_key == moduleKey;
    });
    const moduleLastBox = targetModule.boxes[targetModule.boxes.length - 1];
    addBox('', moduleLastBox, moduleKey);
  };
  // 寻找当前module的最后一个boxKey
  const findModuleLastBoxKey = (modules: any[], boxKey: string) => {
    for (let i = 0; i < modules.length; i++) {
      if (modules[i].boxes.includes(boxKey)) {
        return modules[i].boxes[modules[i].boxes.length - 1];
      }
    }
    return boxKey; // 表示未找到
  };
  // 复制元素
  const copyElement = (type: string, copyEl: any) => {
    let new_el = JSON.parse(JSON.stringify(copyEl));
    new_el.x = parseInt(new_el.x) + 10;
    new_el.y = parseInt(new_el.y) + 10;
    new_el.relative_x_left =
      new_el.relative_x !== null && new_el.relative_key_x !== null
        ? parseInt(new_el.relative_x_left) + 10
        : null;
    new_el.relative_y_top =
      new_el.relative_y !== null && new_el.relative_key_y !== null
        ? parseInt(new_el.relative_y_top) + 10
        : null;
    if (type === 'hotspot') {
      copyHotlink(new_el);
    } else {
      copyConElement(new_el);
    }
  };
  // 复制热区元素
  const copyHotlink = (element: any, synAct = true) => {
    const temp_hot = JSON.parse(
      JSON.stringify(boxes.value[activate_box.value.id].hotLinkList),
    );
    let _activate_box = JSON.parse(JSON.stringify(activate_box.value));
    let _boxes = JSON.parse(JSON.stringify(boxes.value));
    let _elements = JSON.parse(JSON.stringify(elements.value));
    let uuid = guid2('LINK');
    temp_hot.push(uuid);
    element.element_id = uuid;
    if (synAct) {
      _activate_box?.hotLinkList?.push(uuid);
      _boxes[activate_box.value.id].hotLinkList = temp_hot;
    }
    _elements[uuid] = JSON.parse(JSON.stringify(element)); //深克隆element
    setActiveBox(_activate_box);
    setAllBoxes(_boxes);
    setAllElements(_elements);
    return uuid;
  };
  //复制元素
  const copyConElement = (element: any, synAct = true) => {
    let _activate_box = JSON.parse(JSON.stringify(activate_box.value));
    let _boxes = JSON.parse(JSON.stringify(boxes.value));
    let _elements = JSON.parse(JSON.stringify(elements.value));
    let uuid = guid2('EL');
    element.element_id = uuid;
    if (synAct) {
      _activate_box.content.push(uuid);
      _boxes[_activate_box.id].content.push(uuid);
      setAllBoxes(_boxes);
    }
    setActiveBox(_activate_box);
    setAllElements({
      ..._elements,
      [uuid]: JSON.parse(JSON.stringify(element)),
    });
    return uuid;
  };

  // 更新激活状态
  const updateActiveState = (version: any) => {
    // 更新激活模块
    if (modules.value.length > 0) {
      const firstModule = modules.value[0];
      if (!firstModule) return;

      setActiveModule(firstModule);

      // 更新激活画布
      if (firstModule.boxes && firstModule.boxes.length > 0) {
        const firstBoxKey = firstModule.boxes[0];
        if (!firstBoxKey) return;

        // 使用 Record 类型来确保索引访问安全
        const boxesRecord = boxes.value as Record<string, BoxType>;
        const firstBox = boxesRecord[firstBoxKey];
        if (firstBox) {
          // 创建一个新的BoxType对象
          const boxWithKey: BoxType & { key: string } = {
            ...firstBox,
            key: firstBoxKey,
            id: firstBoxKey,
            content: firstBox.content || [],
            hotLinkList: firstBox.hotLinkList || [],
          };

          setActiveBox(boxWithKey, firstModule);

          // 更新激活元素
          if (boxWithKey.content && boxWithKey.content.length > 0) {
            const firstElementKey = boxWithKey.content[0];
            if (!firstElementKey) return;

            // Use Record 类型来确保索引访问安全
            const element = elements.value[activate_el.value.element_id];
            if (element) {
              setActiveElement(element, firstBoxKey, firstModule);
            }
          }
        }
      }
    }
  };

  // 在组件挂载时初始化版本历史
  onMounted(() => {
    initVersionHistory();
  });

  // 监听数据变化,自动保存版本
  watch(
    [boxes, elements],
    () => {
      // 不处于预览和撤销恢复状态,自动保存版本
      if (!isPreview.value && !isRevokeAndRecovery.value) {
        saveVersion();
      }
    },
    {
      deep: true,
      immediate: false, // 防止初始化时触发
    },
  );

  return {
    templateInfo,
    saveTemplate,
    loading,
    error,
    tool,
    fetchTemplate,
    modules, //模块
    activate_module,
    expandedModules,
    updateExpandedModules,
    setActiveModule,
    addModule,
    editModule,
    copyModule,
    delModule,
    moduleMove,
    setAllModules,
    updateModule,
    controlModule,
    addModuleBox,
    boxes, //画布
    activate_box,
    boxesKeys,
    expandedBoxes,
    updateExpandedBoxes,
    setActiveBox,
    setAllBoxes,
    addBox,
    delBox,
    moveBox,
    elements, //元素
    activate_el,
    expandedElements,
    addElement,
    updateExpandedElements,
    setActiveElement,
    setAllElements,
    delElement,
    copyElement,
    updateElement,
    template_info_other_array,
    shop_list,
    handleBackup,
    text_zdy_sep_opt,
    template_field,
    template_title,
    platform_list,
    template_public_image,
    version_record,
    back_version_url,
    text_system_field,
    font,
    getPreview,
    clearPreview,
    sales_brand,
    previewId,
    openPreviewLink,
    previewDataCommon,
    previewData,
    template_excel_hot_value,
    shapeImg,
    shapeImgConfig,
    scrollToActiveBox,
    diff_font,
    id,
    action_shop_page_id,
    setExpandedElement,
    handleRepeal,
    handleRecover,
    canRepeal,
    canRecover,
    saveVersion,
  };
}, {
  // 添加持久化配置
  persist: {
    // 只持久化必要的状态
    paths: ['templateInfo.template_id', 'version'],
    // 或者使用 storage 选项自定义存储方式
    storage: sessionStorage // 使用 sessionStorage 而不是 localStorage
  }
});
