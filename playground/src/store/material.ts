import { defineStore } from 'pinia';
import { ref, watch, nextTick, createVNode, onUnmounted, onMounted } from 'vue';
import {
  getPhysicalCheckInfo,
  postPhysicalCheck,
  deleteErrors,
  postPhysicalPass
} from '#/api/material-check';
import { useRouter } from 'vue-router';
import { message, Modal } from 'ant-design-vue';
import { useAuthStore } from '#/store/auth';
import { 
  ExclamationCircleOutlined
} from '@ant-design/icons-vue';

interface MaterialDetail {
  id: number;
  addEdits: Array<{
    nameFirst: string;
    name: string;
    time: string;
    descText: string;
    operator?: string;
    uploadList?: any[];
  }>;
  [key: string]: any;
}

interface MaterialInfo {
  status: number;
  data: {
    materialDetailList: MaterialDetail[];
    [key: string]: any;
  };
}

export const useMaterialStore = defineStore('material', () => {
   const authStore = useAuthStore();
   const materialList = ref<any[]>([]); //页面总数据
   const loading = ref(false);
   const modalLoading = ref(false); //弹窗的loading
   const errorList = ref<{[key: string]: any[]}>({}); //勾选的异常列表
   const defaultError = [{type: '1', list:[]},{type: '2', new_images:[],error_images:[] },{type: '3', list:[]}]
   const fetchInfo = async (data: any) => {
     try {
      loading.value = true;
      const response = await getPhysicalCheckInfo(data);
      if (response.status != 1) {
        message.error(response.msg || '获取信息失败');
        return;
      }
      return response;
     } catch (err) {
        err instanceof Error ? err.message : 'Failed to fetch template';
    } finally {
      loading.value = false;
      modalLoading.value = false;
    }
   }
   // 页面总数据
   const searchInfo = async(data: any) =>{
    const response = await fetchInfo({
        goods_sn: data.goods_sn,
        action_shop_page_id: data.action_shop_page_id
      })
      console.log('response', response);
    if (!response?.status) {
      return;
    }


    // 检查是否已存在相同goods_sn的数据
    const existingIndex = materialList.value.findIndex(item => item.goods_sn === response.data.goods_sn);
    
    if (existingIndex !== -1) {
      // 如果存在，直接覆盖
      materialList.value[existingIndex] = response.data;
    } else {
      // 如果不存在，向最前面插入
      materialList.value = [response.data, ...materialList.value.slice(0, 4)];
    }
     // 处理errorList
     let goodSn = response.data.goods_sn;
     let errorItem = defaultError;
     if(response.data.check_log && response.data.check_log.diff){
      let diffList = JSON.parse(response.data.check_log.diff)
       errorItem = diffList[diffList.length - 1].error;
     }
     errorList.value[goodSn] = errorItem
    // 滚动到当前条
    setTimeout(() => {
      const element = document.getElementById(data.goods_sn);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
      }
    }, 100); // 添加小延迟确保DOM已更新
   }
   // 添加编辑记录
   const sendAddEdit = async (data: any) => {
     loading.value = true;
     let _error = errorList.value[data.goods_sn] || defaultError
     let Type2Error = _error.find(item => item.type === '2');
     Type2Error.new_images = data.uploadList;
     let targetData = materialList.value.find(item => item.goods_sn === data.goods_sn);
     try { //  id: targetData.check_log.id,
      let params = {
        goods_sn: data.goods_sn,
        status: 2,
        id: targetData.check_log.id,
        diff:{
          content: data.content,
          error: _error
        }
      }
      modalLoading.value = true;
      postPhysicalCheck(params).then(res => {
        modalLoading.value = false;
        if (res.status == 1) {
          searchInfo({goods_sn: data.goods_sn});
        } else {
          message.error('添加失败');
        }
      })
     
       message.success('添加成功');
     } catch (err) {
       message.error('添加失败');
     } finally {
       loading.value = false;
     }
   }
   // 删除编辑记录
   const deleteOneEdit = async (delData: {goods_sn: string, id: string|number, key: string}) => {
      modalLoading.value = true;
      deleteErrors(delData).then(response => {
        if (response?.status == 1) {
          searchInfo({goods_sn: delData.goods_sn});
          message.success('删除成功');
        }else{
          message.error(response?.msg || '删除失败');
        }
      })

   }
   //  删除详情描述整条记录
   const deleteEdit = async (delData: {goods_sn: string, id: string|number}) => {
    try {
      loading.value = true;
      deleteErrors(delData).then(response => {
        loading.value = false;
        if (response?.status == 1) {
            searchInfo({goods_sn: delData.goods_sn});
            message.success('删除成功');
        }else{
            message.error(response?.msg || '删除失败');
        }
      })
    } catch (err) {
      error.value = err instanceof Error ? err.message : '删除失败';
      message.error('删除失败');
    } finally {
      loading.value = false;
    }
   }
   // 校验通过
   const verifyPass = async (detailItem: any) => {
    // 当前商品有异常状态为【未解决】的异常备注时
    Modal.confirm({
      title: '当前商品存在未解决的异常标注，确认标记为校验通过状态吗？',
      icon: createVNode(ExclamationCircleOutlined),
      content: createVNode('div', { style: 'color:red;' }, `当前货号下未解决的异常标注将同步为已解决状态。`),
      async onOk() {
         loading.value = true;
          let targetList = materialList.value.find(item => item.goods_sn === detailItem.goods_sn);
          delete targetList.check_log;
          let params ={
            goods_sn: detailItem.goods_sn,
            status: 1,
            info: targetList
          }
          postPhysicalCheck(params).then(res => {
            loading.value = false;
            if (res.status == 1) {
              searchInfo({goods_sn: detailItem.goods_sn});
            } else {
              message.error('标记为校验通过失败');
            }
          })
      },
      onCancel() {
        console.log('Cancel');
      },
      class: 'test',
    });
   }
   // 二次质检确认
   const secondCheckConfirm = async (data: any) => {
     let params = {
      goods_sn: data.goods_sn,
      status: 3,
      diff: {
        content: data.remarks,
        error: errorList.value[data.goods_sn] || defaultError
      }
     }
      postPhysicalCheck(params).then(res => {
          if (res.status == 1) {
            searchInfo({goods_sn: data.goods_sn});
          } else {
            message.error('确认异常失败');
          }
      }).catch(err => {
        console.log('确认异常请求发送失败', err);
        message.error('确认异常失败');
      })
   }
   // 标记解决
   const toTagAllEdit = async (data: {goods_sn:string, id: string}) => {
     loading.value = true;
     postPhysicalPass(data).then(res => {
      loading.value = false;
        if (res.status == 1) {
          searchInfo({goods_sn: data.goods_sn});
        } else {
          message.error('校验通过失败');
        }
     }).catch(err => {
      console.log('确认异常请求发送失败', err);
      message.error('校验通过失败');
     })
   }
   // set errorList
   const setErrorList = (data: any, goodsSn: string) => {
    errorList.value[goodsSn] = data;
   }
   // 确认异常请求发送
   const confirmAnomaly = async (sn:string, data: any, action_shop_page_id: string) => {
     const goodsSn = sn;
     if (!errorList.value[goodsSn]) {
       errorList.value[goodsSn] = defaultError;
     }
     
     // 将 data.uploadList 赋值给 errorList.value[goodsSn] 中 type: 2 的 new_images
     const type2Error = errorList.value[goodsSn].find(item => item.type === '2');
     if (type2Error && data.uploadList) {
       // 将 uploadList 转换为 new_images 格式
       type2Error.new_images = data.uploadList;
     }
     
     let params = {
      goods_sn: goodsSn,
      status: 2,
      diff:{
        content: data.remark, 
        error: errorList.value[goodsSn]
      }
     }
     loading.value = true;
     const res = await postPhysicalCheck(params).then(res => {
        if (res.status == 1) {
          searchInfo({goods_sn: goodsSn});
        } else {
          message.error('确认异常失败');
        }
        return res;
     }).catch(err => {
      console.log('确认异常请求发送失败', err);
      message.error('确认异常失败');
     })
     return res;
   }
   return {
    materialList,
    loading,
    modalLoading,
    errorList,
    searchInfo,
    fetchInfo,
    sendAddEdit,
    deleteOneEdit,
    deleteEdit,
    verifyPass,
    secondCheckConfirm,
    toTagAllEdit,
    setErrorList,
    confirmAnomaly
   }
});
