import { defineConfig } from 'vite';
import { defineConfig as defineVbenConfig } from '@vben/vite-config';

export default defineConfig(async ({ command, mode }) => {
  // 使用 defineVbenConfig 替代 loadConfig
  const config = await defineVbenConfig(async () => {
    return {
      application: {},
      vite: {},
    };
  })({ command, mode });

  // 优化构建配置
  return {
    ...config,
    build: {
      ...config.build,
      // 启用CSS代码分割
      cssCodeSplit: true,
      // 减小chunk大小
      chunkSizeWarningLimit: 1000,
      rollupOptions: {
        ...config.build?.rollupOptions,
        output: {
          ...config.build?.rollupOptions?.output,
          manualChunks: {
            vendor: ['vue', 'vue-router', 'pinia'],
            'ant-design': ['ant-design-vue'],
            utils: ['@vben/utils'],
          },
        },
      },
      // 启用压缩
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: false,
          drop_debugger: true,
        },
      },
    },
  };
});
